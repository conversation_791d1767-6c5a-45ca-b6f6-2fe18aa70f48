import {
  tooltip_default
} from "./chunk-ER4IFMRV.js";
import "./chunk-VZSNYVQD.js";
import "./chunk-GBX37GKD.js";
import "./chunk-BPAE5FM7.js";
import {
  dynamicApp
} from "./chunk-T627DHLX.js";
import {
  VxeUI
} from "./chunk-KE3GSLX5.js";
import "./chunk-5AGCXRTH.js";
import "./chunk-3IESGBRV.js";
import "./chunk-6RFCLEIL.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.6.35_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/tooltip/index.js
var VxeTooltip = Object.assign({}, tooltip_default, {
  install(app) {
    app.component(tooltip_default.name, tooltip_default);
  }
});
dynamicApp.use(VxeTooltip);
VxeUI.component(tooltip_default);
var Tooltip = VxeTooltip;
var tooltip_default2 = VxeTooltip;

// ../../node_modules/.pnpm/vxe-pc-ui@4.6.35_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-tooltip/index.js
var vxe_tooltip_default = tooltip_default2;
export {
  Tooltip,
  VxeTooltip,
  vxe_tooltip_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-tooltip_index__js.js.map
