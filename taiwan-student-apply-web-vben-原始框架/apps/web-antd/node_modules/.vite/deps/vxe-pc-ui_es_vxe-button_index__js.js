import {
  button_default
} from "./chunk-VKKN2QET.js";
import "./chunk-ER4IFMRV.js";
import "./chunk-VZSNYVQD.js";
import "./chunk-GBX37GKD.js";
import "./chunk-BPAE5FM7.js";
import {
  dynamicApp
} from "./chunk-T627DHLX.js";
import {
  VxeUI
} from "./chunk-KE3GSLX5.js";
import "./chunk-5AGCXRTH.js";
import "./chunk-3IESGBRV.js";
import "./chunk-6RFCLEIL.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.6.35_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/button/index.js
var VxeButton = Object.assign({}, button_default, {
  install(app) {
    app.component(button_default.name, button_default);
  }
});
dynamicApp.use(VxeButton);
VxeUI.component(button_default);
var Button = VxeButton;
var button_default2 = VxeButton;

// ../../node_modules/.pnpm/vxe-pc-ui@4.6.35_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-button/index.js
var vxe_button_default = button_default2;
export {
  Button,
  VxeButton,
  vxe_button_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-button_index__js.js.map
