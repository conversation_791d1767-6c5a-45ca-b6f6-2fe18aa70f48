import {
  column_default
} from "./chunk-HVMGEKWK.js";
import "./chunk-WG5MUYTF.js";
import "./chunk-VSUXYTNC.js";
import "./chunk-KZD657CM.js";
import {
  VxeUI
} from "./chunk-KE3GSLX5.js";
import "./chunk-5AGCXRTH.js";
import "./chunk-3IESGBRV.js";
import "./chunk-6RFCLEIL.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-table@4.13.49_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/column/index.js
var VxeColumn = Object.assign({}, column_default, {
  install(app) {
    app.component(column_default.name, column_default);
    app.component("VxeTableColumn", column_default);
  }
});
if (VxeUI.dynamicApp) {
  VxeUI.dynamicApp.component(column_default.name, column_default);
  VxeUI.dynamicApp.component("VxeTableColumn", column_default);
}
VxeUI.component(column_default);
var Column = VxeColumn;
var column_default2 = VxeColumn;

// ../../node_modules/.pnpm/vxe-table@4.13.49_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/vxe-column/index.js
var vxe_column_default = column_default2;
export {
  Column,
  VxeColumn,
  vxe_column_default as default
};
//# sourceMappingURL=vxe-table_es_vxe-column_index__js.js.map
