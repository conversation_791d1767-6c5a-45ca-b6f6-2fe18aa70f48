import {
  toolbar_default
} from "./chunk-JUO2N4VJ.js";
import "./chunk-VSUXYTNC.js";
import "./chunk-KZD657CM.js";
import {
  VxeUI
} from "./chunk-KE3GSLX5.js";
import "./chunk-5AGCXRTH.js";
import "./chunk-3IESGBRV.js";
import "./chunk-6RFCLEIL.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-table@4.13.49_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/toolbar/index.js
var VxeToolbar = Object.assign({}, toolbar_default, {
  install(app) {
    app.component(toolbar_default.name, toolbar_default);
  }
});
if (VxeUI.dynamicApp) {
  VxeUI.dynamicApp.component(toolbar_default.name, toolbar_default);
}
VxeUI.component(toolbar_default);
var Toolbar = VxeToolbar;
var toolbar_default2 = VxeToolbar;

// ../../node_modules/.pnpm/vxe-table@4.13.49_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-table/es/vxe-toolbar/index.js
var vxe_toolbar_default = toolbar_default2;
export {
  Toolbar,
  VxeToolbar,
  vxe_toolbar_default as default
};
//# sourceMappingURL=vxe-table_es_vxe-toolbar_index__js.js.map
