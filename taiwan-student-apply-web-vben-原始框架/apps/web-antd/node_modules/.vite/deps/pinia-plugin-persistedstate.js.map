{"version": 3, "sources": ["../../../../../node_modules/.pnpm/destr@2.0.5/node_modules/destr/dist/index.mjs", "../../../../../node_modules/.pnpm/deep-pick-omit@1.2.1/node_modules/deep-pick-omit/dist/index.mjs", "../../../../../node_modules/.pnpm/pinia-plugin-persistedstate@4.3.0_magicast@0.3.5_pinia@3.0.3_typescript@5.8.3_vue@3.5.17_typescript@5.8.3__/node_modules/pinia-plugin-persistedstate/dist/index.js"], "sourcesContent": ["const suspectProtoRx = /\"(?:_|\\\\u0{2}5[Ff]){2}(?:p|\\\\u0{2}70)(?:r|\\\\u0{2}72)(?:o|\\\\u0{2}6[Ff])(?:t|\\\\u0{2}74)(?:o|\\\\u0{2}6[Ff])(?:_|\\\\u0{2}5[Ff]){2}\"\\s*:/;\nconst suspectConstructorRx = /\"(?:c|\\\\u0063)(?:o|\\\\u006[Ff])(?:n|\\\\u006[Ee])(?:s|\\\\u0073)(?:t|\\\\u0074)(?:r|\\\\u0072)(?:u|\\\\u0075)(?:c|\\\\u0063)(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:r|\\\\u0072)\"\\s*:/;\nconst JsonSigRx = /^\\s*[\"[{]|^\\s*-?\\d{1,16}(\\.\\d{1,17})?([Ee][+-]?\\d+)?\\s*$/;\nfunction jsonParseTransform(key, value) {\n  if (key === \"__proto__\" || key === \"constructor\" && value && typeof value === \"object\" && \"prototype\" in value) {\n    warnKeyDropped(key);\n    return;\n  }\n  return value;\n}\nfunction warnKeyDropped(key) {\n  console.warn(`[destr] Dropping \"${key}\" key to prevent prototype pollution.`);\n}\nfunction destr(value, options = {}) {\n  if (typeof value !== \"string\") {\n    return value;\n  }\n  if (value[0] === '\"' && value[value.length - 1] === '\"' && value.indexOf(\"\\\\\") === -1) {\n    return value.slice(1, -1);\n  }\n  const _value = value.trim();\n  if (_value.length <= 9) {\n    switch (_value.toLowerCase()) {\n      case \"true\": {\n        return true;\n      }\n      case \"false\": {\n        return false;\n      }\n      case \"undefined\": {\n        return void 0;\n      }\n      case \"null\": {\n        return null;\n      }\n      case \"nan\": {\n        return Number.NaN;\n      }\n      case \"infinity\": {\n        return Number.POSITIVE_INFINITY;\n      }\n      case \"-infinity\": {\n        return Number.NEGATIVE_INFINITY;\n      }\n    }\n  }\n  if (!JsonSigRx.test(value)) {\n    if (options.strict) {\n      throw new SyntaxError(\"[destr] Invalid JSON\");\n    }\n    return value;\n  }\n  try {\n    if (suspectProtoRx.test(value) || suspectConstructorRx.test(value)) {\n      if (options.strict) {\n        throw new Error(\"[destr] Possible prototype pollution\");\n      }\n      return JSON.parse(value, jsonParseTransform);\n    }\n    return JSON.parse(value);\n  } catch (error) {\n    if (options.strict) {\n      throw error;\n    }\n    return value;\n  }\n}\nfunction safeDestr(value, options = {}) {\n  return destr(value, { ...options, strict: true });\n}\n\nexport { destr as default, destr, safeDestr };\n", "function get(obj, path) {\n  if (obj == null)\n    return void 0;\n  let value = obj;\n  for (let i = 0; i < path.length; i++) {\n    if (value == null || value[path[i]] == null)\n      return void 0;\n    value = value[path[i]];\n  }\n  return value;\n}\nfunction set(obj, value, path) {\n  if (path.length === 0)\n    return value;\n  const idx = path[0];\n  if (path.length > 1) {\n    value = set(\n      typeof obj !== \"object\" || obj === null || !Object.prototype.hasOwnProperty.call(obj, idx) ? Number.isInteger(Number(path[1])) ? [] : {} : obj[idx],\n      value,\n      Array.prototype.slice.call(path, 1)\n    );\n  }\n  if (Number.isInteger(Number(idx)) && Array.isArray(obj))\n    return obj.slice()[idx];\n  return Object.assign({}, obj, { [idx]: value });\n}\nfunction unset(obj, path) {\n  if (obj == null || path.length === 0)\n    return obj;\n  if (path.length === 1) {\n    if (obj == null)\n      return obj;\n    if (Number.isInteger(path[0]) && Array.isArray(obj))\n      return Array.prototype.slice.call(obj, 0).splice(path[0], 1);\n    const result = {};\n    for (const p in obj)\n      result[p] = obj[p];\n    delete result[path[0]];\n    return result;\n  }\n  if (obj[path[0]] == null) {\n    if (Number.isInteger(path[0]) && Array.isArray(obj))\n      return Array.prototype.concat.call([], obj);\n    const result = {};\n    for (const p in obj)\n      result[p] = obj[p];\n    return result;\n  }\n  return set(\n    obj,\n    unset(\n      obj[path[0]],\n      Array.prototype.slice.call(path, 1)\n    ),\n    [path[0]]\n  );\n}\n\nfunction deepPickUnsafe(obj, paths) {\n  return paths.map((p) => p.split(\".\")).map((p) => [p, get(obj, p)]).filter((t) => t[1] !== void 0).reduce((acc, cur) => set(acc, cur[1], cur[0]), {});\n}\nfunction deepPick(obj, paths) {\n  return deepPickUnsafe(obj, paths);\n}\nfunction deepOmitUnsafe(obj, paths) {\n  return paths.map((p) => p.split(\".\")).reduce((acc, cur) => unset(acc, cur), obj);\n}\nfunction deepOmit(obj, paths) {\n  return deepOmitUnsafe(obj, paths);\n}\n\nexport { deepOmit, deepOmitUnsafe, deepPick, deepPickUnsafe };\n", "// src/index.ts\nimport { destr } from \"destr\";\n\n// src/runtime/core.ts\nimport { deepOmitUnsafe, deepPickUnsafe } from \"deep-pick-omit\";\nfunction hydrateStore(store, {\n  storage,\n  serializer,\n  key,\n  debug,\n  pick,\n  omit,\n  beforeHydrate,\n  afterHydrate\n}, context, runHooks = true) {\n  try {\n    if (runHooks)\n      beforeHydrate?.(context);\n    const fromStorage = storage.getItem(key);\n    if (fromStorage) {\n      const deserialized = serializer.deserialize(fromStorage);\n      const picked = pick ? deepPickUnsafe(deserialized, pick) : deserialized;\n      const omitted = omit ? deepOmitUnsafe(picked, omit) : picked;\n      store.$patch(omitted);\n    }\n    if (runHooks)\n      afterHydrate?.(context);\n  } catch (error) {\n    if (debug)\n      console.error(\"[pinia-plugin-persistedstate]\", error);\n  }\n}\nfunction persistState(state, {\n  storage,\n  serializer,\n  key,\n  debug,\n  pick,\n  omit\n}) {\n  try {\n    const picked = pick ? deepPickUnsafe(state, pick) : state;\n    const omitted = omit ? deepOmitUnsafe(picked, omit) : picked;\n    const toStorage = serializer.serialize(omitted);\n    storage.setItem(key, toStorage);\n  } catch (error) {\n    if (debug)\n      console.error(\"[pinia-plugin-persistedstate]\", error);\n  }\n}\nfunction createPersistence(context, optionsParser, auto) {\n  const { pinia, store, options: { persist = auto } } = context;\n  if (!persist)\n    return;\n  if (!(store.$id in pinia.state.value)) {\n    const originalStore = pinia._s.get(store.$id.replace(\"__hot:\", \"\"));\n    if (originalStore)\n      void Promise.resolve().then(() => originalStore.$persist());\n    return;\n  }\n  const persistenceOptions = Array.isArray(persist) ? persist : persist === true ? [{}] : [persist];\n  const persistences = persistenceOptions.map(optionsParser);\n  store.$hydrate = ({ runHooks = true } = {}) => {\n    persistences.forEach((p) => {\n      hydrateStore(store, p, context, runHooks);\n    });\n  };\n  store.$persist = () => {\n    persistences.forEach((p) => {\n      persistState(store.$state, p);\n    });\n  };\n  persistences.forEach((p) => {\n    hydrateStore(store, p, context);\n    store.$subscribe(\n      (_mutation, state) => persistState(state, p),\n      { detached: true }\n    );\n  });\n}\n\n// src/index.ts\nfunction createPersistedState(options = {}) {\n  return function(context) {\n    createPersistence(\n      context,\n      (p) => ({\n        key: (options.key ? options.key : (x) => x)(p.key ?? context.store.$id),\n        debug: p.debug ?? options.debug ?? false,\n        serializer: p.serializer ?? options.serializer ?? {\n          serialize: (data) => JSON.stringify(data),\n          deserialize: (data) => destr(data)\n        },\n        storage: p.storage ?? options.storage ?? window.localStorage,\n        beforeHydrate: p.beforeHydrate,\n        afterHydrate: p.afterHydrate,\n        pick: p.pick,\n        omit: p.omit\n      }),\n      options.auto ?? false\n    );\n  };\n}\nvar index_default = createPersistedState();\nexport {\n  createPersistedState,\n  index_default as default\n};\n"], "mappings": ";;;AAAA,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAC7B,IAAM,YAAY;AAClB,SAAS,mBAAmB,KAAK,OAAO;AACtC,MAAI,QAAQ,eAAe,QAAQ,iBAAiB,SAAS,OAAO,UAAU,YAAY,eAAe,OAAO;AAC9G,mBAAe,GAAG;AAClB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,KAAK;AAC3B,UAAQ,KAAK,qBAAqB,GAAG,uCAAuC;AAC9E;AACA,SAAS,MAAM,OAAO,UAAU,CAAC,GAAG;AAClC,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AACA,MAAI,MAAM,CAAC,MAAM,OAAO,MAAM,MAAM,SAAS,CAAC,MAAM,OAAO,MAAM,QAAQ,IAAI,MAAM,IAAI;AACrF,WAAO,MAAM,MAAM,GAAG,EAAE;AAAA,EAC1B;AACA,QAAM,SAAS,MAAM,KAAK;AAC1B,MAAI,OAAO,UAAU,GAAG;AACtB,YAAQ,OAAO,YAAY,GAAG;AAAA,MAC5B,KAAK,QAAQ;AACX,eAAO;AAAA,MACT;AAAA,MACA,KAAK,SAAS;AACZ,eAAO;AAAA,MACT;AAAA,MACA,KAAK,aAAa;AAChB,eAAO;AAAA,MACT;AAAA,MACA,KAAK,QAAQ;AACX,eAAO;AAAA,MACT;AAAA,MACA,KAAK,OAAO;AACV,eAAO,OAAO;AAAA,MAChB;AAAA,MACA,KAAK,YAAY;AACf,eAAO,OAAO;AAAA,MAChB;AAAA,MACA,KAAK,aAAa;AAChB,eAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AAC1B,QAAI,QAAQ,QAAQ;AAClB,YAAM,IAAI,YAAY,sBAAsB;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AACA,MAAI;AACF,QAAI,eAAe,KAAK,KAAK,KAAK,qBAAqB,KAAK,KAAK,GAAG;AAClE,UAAI,QAAQ,QAAQ;AAClB,cAAM,IAAI,MAAM,sCAAsC;AAAA,MACxD;AACA,aAAO,KAAK,MAAM,OAAO,kBAAkB;AAAA,IAC7C;AACA,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB,SAAS,OAAO;AACd,QAAI,QAAQ,QAAQ;AAClB,YAAM;AAAA,IACR;AACA,WAAO;AAAA,EACT;AACF;;;AClEA,SAAS,IAAI,KAAK,MAAM;AACtB,MAAI,OAAO;AACT,WAAO;AACT,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,SAAS,QAAQ,MAAM,KAAK,CAAC,CAAC,KAAK;AACrC,aAAO;AACT,YAAQ,MAAM,KAAK,CAAC,CAAC;AAAA,EACvB;AACA,SAAO;AACT;AACA,SAAS,IAAI,KAAK,OAAO,MAAM;AAC7B,MAAI,KAAK,WAAW;AAClB,WAAO;AACT,QAAM,MAAM,KAAK,CAAC;AAClB,MAAI,KAAK,SAAS,GAAG;AACnB,YAAQ;AAAA,MACN,OAAO,QAAQ,YAAY,QAAQ,QAAQ,CAAC,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG,IAAI,OAAO,UAAU,OAAO,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG;AAAA,MAClJ;AAAA,MACA,MAAM,UAAU,MAAM,KAAK,MAAM,CAAC;AAAA,IACpC;AAAA,EACF;AACA,MAAI,OAAO,UAAU,OAAO,GAAG,CAAC,KAAK,MAAM,QAAQ,GAAG;AACpD,WAAO,IAAI,MAAM,EAAE,GAAG;AACxB,SAAO,OAAO,OAAO,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC;AAChD;AACA,SAAS,MAAM,KAAK,MAAM;AACxB,MAAI,OAAO,QAAQ,KAAK,WAAW;AACjC,WAAO;AACT,MAAI,KAAK,WAAW,GAAG;AACrB,QAAI,OAAO;AACT,aAAO;AACT,QAAI,OAAO,UAAU,KAAK,CAAC,CAAC,KAAK,MAAM,QAAQ,GAAG;AAChD,aAAO,MAAM,UAAU,MAAM,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC;AAC7D,UAAM,SAAS,CAAC;AAChB,eAAW,KAAK;AACd,aAAO,CAAC,IAAI,IAAI,CAAC;AACnB,WAAO,OAAO,KAAK,CAAC,CAAC;AACrB,WAAO;AAAA,EACT;AACA,MAAI,IAAI,KAAK,CAAC,CAAC,KAAK,MAAM;AACxB,QAAI,OAAO,UAAU,KAAK,CAAC,CAAC,KAAK,MAAM,QAAQ,GAAG;AAChD,aAAO,MAAM,UAAU,OAAO,KAAK,CAAC,GAAG,GAAG;AAC5C,UAAM,SAAS,CAAC;AAChB,eAAW,KAAK;AACd,aAAO,CAAC,IAAI,IAAI,CAAC;AACnB,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,MACE,IAAI,KAAK,CAAC,CAAC;AAAA,MACX,MAAM,UAAU,MAAM,KAAK,MAAM,CAAC;AAAA,IACpC;AAAA,IACA,CAAC,KAAK,CAAC,CAAC;AAAA,EACV;AACF;AAEA,SAAS,eAAe,KAAK,OAAO;AAClC,SAAO,MAAM,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,KAAK,QAAQ,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACrJ;AAIA,SAAS,eAAe,KAAK,OAAO;AAClC,SAAO,MAAM,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,GAAG,GAAG,GAAG;AACjF;;;AC7DA,SAAS,aAAa,OAAO;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG,SAAS,WAAW,MAAM;AAC3B,MAAI;AACF,QAAI;AACF,qDAAgB;AAClB,UAAM,cAAc,QAAQ,QAAQ,GAAG;AACvC,QAAI,aAAa;AACf,YAAM,eAAe,WAAW,YAAY,WAAW;AACvD,YAAM,SAAS,OAAO,eAAe,cAAc,IAAI,IAAI;AAC3D,YAAM,UAAU,OAAO,eAAe,QAAQ,IAAI,IAAI;AACtD,YAAM,OAAO,OAAO;AAAA,IACtB;AACA,QAAI;AACF,mDAAe;AAAA,EACnB,SAAS,OAAO;AACd,QAAI;AACF,cAAQ,MAAM,iCAAiC,KAAK;AAAA,EACxD;AACF;AACA,SAAS,aAAa,OAAO;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,MAAI;AACF,UAAM,SAAS,OAAO,eAAe,OAAO,IAAI,IAAI;AACpD,UAAM,UAAU,OAAO,eAAe,QAAQ,IAAI,IAAI;AACtD,UAAM,YAAY,WAAW,UAAU,OAAO;AAC9C,YAAQ,QAAQ,KAAK,SAAS;AAAA,EAChC,SAAS,OAAO;AACd,QAAI;AACF,cAAQ,MAAM,iCAAiC,KAAK;AAAA,EACxD;AACF;AACA,SAAS,kBAAkB,SAAS,eAAe,MAAM;AACvD,QAAM,EAAE,OAAO,OAAO,SAAS,EAAE,UAAU,KAAK,EAAE,IAAI;AACtD,MAAI,CAAC;AACH;AACF,MAAI,EAAE,MAAM,OAAO,MAAM,MAAM,QAAQ;AACrC,UAAM,gBAAgB,MAAM,GAAG,IAAI,MAAM,IAAI,QAAQ,UAAU,EAAE,CAAC;AAClE,QAAI;AACF,WAAK,QAAQ,QAAQ,EAAE,KAAK,MAAM,cAAc,SAAS,CAAC;AAC5D;AAAA,EACF;AACA,QAAM,qBAAqB,MAAM,QAAQ,OAAO,IAAI,UAAU,YAAY,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO;AAChG,QAAM,eAAe,mBAAmB,IAAI,aAAa;AACzD,QAAM,WAAW,CAAC,EAAE,WAAW,KAAK,IAAI,CAAC,MAAM;AAC7C,iBAAa,QAAQ,CAAC,MAAM;AAC1B,mBAAa,OAAO,GAAG,SAAS,QAAQ;AAAA,IAC1C,CAAC;AAAA,EACH;AACA,QAAM,WAAW,MAAM;AACrB,iBAAa,QAAQ,CAAC,MAAM;AAC1B,mBAAa,MAAM,QAAQ,CAAC;AAAA,IAC9B,CAAC;AAAA,EACH;AACA,eAAa,QAAQ,CAAC,MAAM;AAC1B,iBAAa,OAAO,GAAG,OAAO;AAC9B,UAAM;AAAA,MACJ,CAAC,WAAW,UAAU,aAAa,OAAO,CAAC;AAAA,MAC3C,EAAE,UAAU,KAAK;AAAA,IACnB;AAAA,EACF,CAAC;AACH;AAGA,SAAS,qBAAqB,UAAU,CAAC,GAAG;AAC1C,SAAO,SAAS,SAAS;AACvB;AAAA,MACE;AAAA,MACA,CAAC,OAAO;AAAA,QACN,MAAM,QAAQ,MAAM,QAAQ,MAAM,CAAC,MAAM,GAAG,EAAE,OAAO,QAAQ,MAAM,GAAG;AAAA,QACtE,OAAO,EAAE,SAAS,QAAQ,SAAS;AAAA,QACnC,YAAY,EAAE,cAAc,QAAQ,cAAc;AAAA,UAChD,WAAW,CAAC,SAAS,KAAK,UAAU,IAAI;AAAA,UACxC,aAAa,CAAC,SAAS,MAAM,IAAI;AAAA,QACnC;AAAA,QACA,SAAS,EAAE,WAAW,QAAQ,WAAW,OAAO;AAAA,QAChD,eAAe,EAAE;AAAA,QACjB,cAAc,EAAE;AAAA,QAChB,MAAM,EAAE;AAAA,QACR,MAAM,EAAE;AAAA,MACV;AAAA,MACA,QAAQ,QAAQ;AAAA,IAClB;AAAA,EACF;AACF;AACA,IAAI,gBAAgB,qBAAqB;", "names": []}