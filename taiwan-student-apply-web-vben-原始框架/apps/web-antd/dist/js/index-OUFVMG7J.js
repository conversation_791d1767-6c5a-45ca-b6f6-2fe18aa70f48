import{_ as h,P as b,b as ee,j as ne,Q as ut,K as R,F as _t,i as jt,f as Yt,x as te,a9 as le,aa as Ut,C as Xt,X as Gt,a2 as Qt,r as Ae,J as ze,m as ie,D as ct,k as Jt,A as Zt}from"./bootstrap-CMNRQ0xm.js";import{T as qt}from"./Trigger-UKZ_Pgvu.js";import{a4 as re,J as z,P as pe,x as y,al as dt,ba as kt,R as We,aF as Ke,a5 as A,ao as _e,a9 as ae,Y as G,ai as Ve,F as $e,az as ye,U as en,T as be,Z as tn,ax as nn,n as He,p as on,r as ln}from"../jse/index-index-DjeMElj0.js";import{c as ft}from"./vnode-BlmdA_v_.js";import{B as an}from"./BaseInput-D6vkoer7.js";import{O as rn}from"./Overflow-DIST5fzu.js";import{i as sn}from"./isMobile-8sZ0LT6r.js";import{R as un}from"./index-BqJHF-Vt.js";import{D as cn}from"./DownOutlined-Bh1d3nlW.js";import{C as dn}from"./CheckOutlined-CLMjitcS.js";import{S as fn}from"./SearchOutlined-CEwpwteP.js";import{i as nt}from"./move-UyZYHWoW.js";import{i as ot,a as pn,s as mn,c as gn,b as hn}from"./slide-DmwdwDp9.js";function lt(e,t){const{key:n}=e;let o;return"value"in e&&({value:o}=e),n!=null?n:o!==void 0?o:`rc-index-key-${t}`}function vn(e,t){const{label:n,value:o,options:l}=e||{};return{label:n||(t?"children":"label"),value:o||"value",options:l||"options"}}function Eo(e){let{fieldNames:t,childrenAsData:n}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const o=[],{label:l,value:i,options:c}=vn(t,!1);function r(p,a){p.forEach(s=>{const g=s[l];if(a||!(c in s)){const d=s[i];o.push({key:lt(s,o.length),groupOption:a,data:s,label:g,value:d})}else{let d=g;d===void 0&&n&&(d=s.label),o.push({key:lt(s,o.length),group:!0,data:s,label:d}),r(s[c],!0)}})}return r(e,!1),o}function Ro(e){const t=h({},e);return"props"in t||Object.defineProperty(t,"props",{get(){return t}}),t}function Sn(e,t){if(!t||!t.length)return null;let n=!1;function o(i,c){let[r,...p]=c;if(!r)return[i];const a=i.split(r);return n=n||a.length>1,a.reduce((s,g)=>[...s,...o(g,p)],[]).filter(s=>s)}const l=o(e,t);return n?l:null}var bn=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,o=Object.getOwnPropertySymbols(e);l<o.length;l++)t.indexOf(o[l])<0&&Object.prototype.propertyIsEnumerable.call(e,o[l])&&(n[o[l]]=e[o[l]]);return n};const yn=e=>{const t=e===!0?0:1;return{bottomLeft:{points:["tl","bl"],offset:[0,4],overflow:{adjustX:t,adjustY:1}},bottomRight:{points:["tr","br"],offset:[0,4],overflow:{adjustX:t,adjustY:1}},topLeft:{points:["bl","tl"],offset:[0,-4],overflow:{adjustX:t,adjustY:1}},topRight:{points:["br","tr"],offset:[0,-4],overflow:{adjustX:t,adjustY:1}}}},wn=re({name:"SelectTrigger",inheritAttrs:!1,props:{dropdownAlign:Object,visible:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},dropdownClassName:String,dropdownStyle:b.object,placement:String,empty:{type:Boolean,default:void 0},prefixCls:String,popupClassName:String,animation:String,transitionName:String,getPopupContainer:Function,dropdownRender:Function,containerWidth:Number,dropdownMatchSelectWidth:b.oneOfType([Number,Boolean]).def(!0),popupElement:b.any,direction:String,getTriggerDOMNode:Function,onPopupVisibleChange:Function,onPopupMouseEnter:Function,onPopupFocusin:Function,onPopupFocusout:Function},setup(e,t){let{slots:n,attrs:o,expose:l}=t;const i=z(()=>{const{dropdownMatchSelectWidth:r}=e;return yn(r)}),c=pe();return l({getPopupElement:()=>c.value}),()=>{const r=h(h({},e),o),{empty:p=!1}=r,a=bn(r,["empty"]),{visible:s,dropdownAlign:g,prefixCls:d,popupElement:M,dropdownClassName:v,dropdownStyle:$,direction:I="ltr",placement:S,dropdownMatchSelectWidth:w,containerWidth:E,dropdownRender:x,animation:D,transitionName:F,getPopupContainer:T,getTriggerDOMNode:V,onPopupVisibleChange:O,onPopupMouseEnter:L,onPopupFocusin:P,onPopupFocusout:B}=a,W=`${d}-dropdown`;let j=M;x&&(j=x({menuNode:M,props:e}));const H=D?`${W}-${D}`:F,K=h({minWidth:`${E}px`},$);return typeof w=="number"?K.width=`${w}px`:w&&(K.width=`${E}px`),y(qt,ee(ee({},e),{},{showAction:O?["click"]:[],hideAction:O?["click"]:[],popupPlacement:S||(I==="rtl"?"bottomRight":"bottomLeft"),builtinPlacements:i.value,prefixCls:W,popupTransitionName:H,popupAlign:g,popupVisible:s,getPopupContainer:T,popupClassName:ne(v,{[`${W}-empty`]:p}),popupStyle:K,getTriggerDOMNode:V,onPopupVisibleChange:O}),{default:n.default,popup:()=>y("div",{ref:c,onMouseenter:L,onFocusin:P,onFocusout:B},[j])})}}}),ge=(e,t)=>{let{slots:n}=t;var o;const{class:l,customizeIcon:i,customizeIconProps:c,onMousedown:r,onClick:p}=e;let a;return typeof i=="function"?a=i(c):a=kt(i)?dt(i):i,y("span",{class:l,onMousedown:s=>{s.preventDefault(),r&&r(s)},style:{userSelect:"none",WebkitUserSelect:"none"},unselectable:"on",onClick:p,"aria-hidden":!0},[a!==void 0?a:y("span",{class:l.split(/\s+/).map(s=>`${s}-icon`)},[(o=n.default)===null||o===void 0?void 0:o.call(n)])])};ge.inheritAttrs=!1;ge.displayName="TransBtn";ge.props={class:String,customizeIcon:b.any,customizeIconProps:b.any,onMousedown:Function,onClick:Function};const xn={inputRef:b.any,prefixCls:String,id:String,inputElement:b.VueNode,disabled:{type:Boolean,default:void 0},autofocus:{type:Boolean,default:void 0},autocomplete:String,editable:{type:Boolean,default:void 0},activeDescendantId:String,value:String,open:{type:Boolean,default:void 0},tabindex:b.oneOfType([b.number,b.string]),attrs:b.object,onKeydown:{type:Function},onMousedown:{type:Function},onChange:{type:Function},onPaste:{type:Function},onCompositionstart:{type:Function},onCompositionend:{type:Function},onFocus:{type:Function},onBlur:{type:Function}},pt=re({compatConfig:{MODE:3},name:"SelectInput",inheritAttrs:!1,props:xn,setup(e){let t=null;const n=We("VCSelectContainerEvent");return()=>{var o;const{prefixCls:l,id:i,inputElement:c,disabled:r,tabindex:p,autofocus:a,autocomplete:s,editable:g,activeDescendantId:d,value:M,onKeydown:v,onMousedown:$,onChange:I,onPaste:S,onCompositionstart:w,onCompositionend:E,onFocus:x,onBlur:D,open:F,inputRef:T,attrs:V}=e;let O=c||y(an,null,null);const L=O.props||{},{onKeydown:P,onInput:B,onFocus:W,onBlur:j,onMousedown:H,onCompositionstart:K,onCompositionend:oe,style:se}=L;return O=ft(O,h(h(h(h(h({type:"search"},L),{id:i,ref:T,disabled:r,tabindex:p,lazy:!1,autocomplete:s||"off",autofocus:a,class:ne(`${l}-selection-search-input`,(o=O==null?void 0:O.props)===null||o===void 0?void 0:o.class),role:"combobox","aria-expanded":F,"aria-haspopup":"listbox","aria-owns":`${i}_list`,"aria-autocomplete":"list","aria-controls":`${i}_list`,"aria-activedescendant":d}),V),{value:g?M:"",readonly:!g,unselectable:g?null:"on",style:h(h({},se),{opacity:g?null:0}),onKeydown:u=>{v(u),P&&P(u)},onMousedown:u=>{$(u),H&&H(u)},onInput:u=>{I(u),B&&B(u)},onCompositionstart(u){w(u),K&&K(u)},onCompositionend(u){E(u),oe&&oe(u)},onPaste:S,onFocus:function(){clearTimeout(t),W&&W(arguments.length<=0?void 0:arguments[0]),x&&x(arguments.length<=0?void 0:arguments[0]),n==null||n.focus(arguments.length<=0?void 0:arguments[0])},onBlur:function(){for(var u=arguments.length,C=new Array(u),N=0;N<u;N++)C[N]=arguments[N];t=setTimeout(()=>{j&&j(C[0]),D&&D(C[0]),n==null||n.blur(C[0])},100)}}),O.type==="textarea"?{}:{type:"search"}),!0,!0),O}}}),mt=Symbol("TreeSelectLegacyContextPropsKey");function Fo(e){return Ke(mt,e)}function je(){return We(mt,{})}const In={id:String,prefixCls:String,values:b.array,open:{type:Boolean,default:void 0},searchValue:String,inputRef:b.any,placeholder:b.any,disabled:{type:Boolean,default:void 0},mode:String,showSearch:{type:Boolean,default:void 0},autofocus:{type:Boolean,default:void 0},autocomplete:String,activeDescendantId:String,tabindex:b.oneOfType([b.number,b.string]),compositionStatus:Boolean,removeIcon:b.any,choiceTransitionName:String,maxTagCount:b.oneOfType([b.number,b.string]),maxTagTextLength:Number,maxTagPlaceholder:b.any.def(()=>e=>`+ ${e.length} ...`),tagRender:Function,onToggleOpen:{type:Function},onRemove:Function,onInputChange:Function,onInputPaste:Function,onInputKeyDown:Function,onInputMouseDown:Function,onInputCompositionStart:Function,onInputCompositionEnd:Function},it=e=>{e.preventDefault(),e.stopPropagation()},Cn=re({name:"MultipleSelectSelector",inheritAttrs:!1,props:In,setup(e){const t=A(),n=A(0),o=A(!1),l=je(),i=z(()=>`${e.prefixCls}-selection`),c=z(()=>e.open||e.mode==="tags"?e.searchValue:""),r=z(()=>e.mode==="tags"||e.showSearch&&(e.open||o.value)),p=pe("");_e(()=>{p.value=c.value}),ae(()=>{G(p,()=>{n.value=t.value.scrollWidth},{flush:"post",immediate:!0})});function a(v,$,I,S,w){return y("span",{class:ne(`${i.value}-item`,{[`${i.value}-item-disabled`]:I}),title:typeof v=="string"||typeof v=="number"?v.toString():void 0},[y("span",{class:`${i.value}-item-content`},[$]),S&&y(ge,{class:`${i.value}-item-remove`,onMousedown:it,onClick:w,customizeIcon:e.removeIcon},{default:()=>[Ve("×")]})])}function s(v,$,I,S,w,E){var x;const D=T=>{it(T),e.onToggleOpen(!open)};let F=E;return l.keyEntities&&(F=((x=l.keyEntities[v])===null||x===void 0?void 0:x.node)||{}),y("span",{key:v,onMousedown:D},[e.tagRender({label:$,value:v,disabled:I,closable:S,onClose:w,option:F})])}function g(v){const{disabled:$,label:I,value:S,option:w}=v,E=!e.disabled&&!$;let x=I;if(typeof e.maxTagTextLength=="number"&&(typeof I=="string"||typeof I=="number")){const F=String(x);F.length>e.maxTagTextLength&&(x=`${F.slice(0,e.maxTagTextLength)}...`)}const D=F=>{var T;F&&F.stopPropagation(),(T=e.onRemove)===null||T===void 0||T.call(e,v)};return typeof e.tagRender=="function"?s(S,x,$,E,D,w):a(I,x,$,E,D)}function d(v){const{maxTagPlaceholder:$=S=>`+ ${S.length} ...`}=e,I=typeof $=="function"?$(v):$;return a(I,I,!1)}const M=v=>{const $=v.target.composing;p.value=v.target.value,$||e.onInputChange(v)};return()=>{const{id:v,prefixCls:$,values:I,open:S,inputRef:w,placeholder:E,disabled:x,autofocus:D,autocomplete:F,activeDescendantId:T,tabindex:V,compositionStatus:O,onInputPaste:L,onInputKeyDown:P,onInputMouseDown:B,onInputCompositionStart:W,onInputCompositionEnd:j}=e,H=y("div",{class:`${i.value}-search`,style:{width:n.value+"px"},key:"input"},[y(pt,{inputRef:w,open:S,prefixCls:$,id:v,inputElement:null,disabled:x,autofocus:D,autocomplete:F,editable:r.value,activeDescendantId:T,value:p.value,onKeydown:P,onMousedown:B,onChange:M,onPaste:L,onCompositionstart:W,onCompositionend:j,tabindex:V,attrs:ut(e,!0),onFocus:()=>o.value=!0,onBlur:()=>o.value=!1},null),y("span",{ref:t,class:`${i.value}-search-mirror`,"aria-hidden":!0},[p.value,Ve(" ")])]),K=y(rn,{prefixCls:`${i.value}-overflow`,data:I,renderItem:g,renderRest:d,suffix:H,itemKey:"key",maxCount:e.maxTagCount,key:"overflow"},null);return y($e,null,[K,!I.length&&!c.value&&!O&&y("span",{class:`${i.value}-placeholder`},[E])])}}}),$n={inputElement:b.any,id:String,prefixCls:String,values:b.array,open:{type:Boolean,default:void 0},searchValue:String,inputRef:b.any,placeholder:b.any,compositionStatus:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},mode:String,showSearch:{type:Boolean,default:void 0},autofocus:{type:Boolean,default:void 0},autocomplete:String,activeDescendantId:String,tabindex:b.oneOfType([b.number,b.string]),activeValue:String,backfill:{type:Boolean,default:void 0},optionLabelRender:Function,onInputChange:Function,onInputPaste:Function,onInputKeyDown:Function,onInputMouseDown:Function,onInputCompositionStart:Function,onInputCompositionEnd:Function},Ye=re({name:"SingleSelector",setup(e){const t=A(!1),n=z(()=>e.mode==="combobox"),o=z(()=>n.value||e.showSearch),l=z(()=>{let s=e.searchValue||"";return n.value&&e.activeValue&&!t.value&&(s=e.activeValue),s}),i=je();G([n,()=>e.activeValue],()=>{n.value&&(t.value=!1)},{immediate:!0});const c=z(()=>e.mode!=="combobox"&&!e.open&&!e.showSearch?!1:!!l.value||e.compositionStatus),r=z(()=>{const s=e.values[0];return s&&(typeof s.label=="string"||typeof s.label=="number")?s.label.toString():void 0}),p=()=>{if(e.values[0])return null;const s=c.value?{visibility:"hidden"}:void 0;return y("span",{class:`${e.prefixCls}-selection-placeholder`,style:s},[e.placeholder])},a=s=>{s.target.composing||(t.value=!0,e.onInputChange(s))};return()=>{var s,g,d,M;const{inputElement:v,prefixCls:$,id:I,values:S,inputRef:w,disabled:E,autofocus:x,autocomplete:D,activeDescendantId:F,open:T,tabindex:V,optionLabelRender:O,onInputKeyDown:L,onInputMouseDown:P,onInputPaste:B,onInputCompositionStart:W,onInputCompositionEnd:j}=e,H=S[0];let K=null;if(H&&i.customSlots){const oe=(s=H.key)!==null&&s!==void 0?s:H.value,se=((g=i.keyEntities[oe])===null||g===void 0?void 0:g.node)||{};K=i.customSlots[(d=se.slots)===null||d===void 0?void 0:d.title]||i.customSlots.title||H.label,typeof K=="function"&&(K=K(se))}else K=O&&H?O(H.option):H==null?void 0:H.label;return y($e,null,[y("span",{class:`${$}-selection-search`},[y(pt,{inputRef:w,prefixCls:$,id:I,open:T,inputElement:v,disabled:E,autofocus:x,autocomplete:D,editable:o.value,activeDescendantId:F,value:l.value,onKeydown:L,onMousedown:P,onChange:a,onPaste:B,onCompositionstart:W,onCompositionend:j,tabindex:V,attrs:ut(e,!0)},null)]),!n.value&&H&&!c.value&&y("span",{class:`${$}-selection-item`,title:r.value},[y($e,{key:(M=H.key)!==null&&M!==void 0?M:H.value},[K])]),p()])}}});Ye.props=$n;Ye.inheritAttrs=!1;function Tn(e){return![R.ESC,R.SHIFT,R.BACKSPACE,R.TAB,R.WIN_KEY,R.ALT,R.META,R.WIN_KEY_RIGHT,R.CTRL,R.SEMICOLON,R.EQUALS,R.CAPS_LOCK,R.CONTEXT_MENU,R.F1,R.F2,R.F3,R.F4,R.F5,R.F6,R.F7,R.F8,R.F9,R.F10,R.F11,R.F12].includes(e)}function gt(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:250,t=null,n;ye(()=>{clearTimeout(n)});function o(l){(l||t===null)&&(t=l),clearTimeout(n),n=setTimeout(()=>{t=null},e)}return[()=>t,o]}function Te(){const e=t=>{e.current=t};return e}const Mn=re({name:"Selector",inheritAttrs:!1,props:{id:String,prefixCls:String,showSearch:{type:Boolean,default:void 0},open:{type:Boolean,default:void 0},values:b.array,multiple:{type:Boolean,default:void 0},mode:String,searchValue:String,activeValue:String,inputElement:b.any,autofocus:{type:Boolean,default:void 0},activeDescendantId:String,tabindex:b.oneOfType([b.number,b.string]),disabled:{type:Boolean,default:void 0},placeholder:b.any,removeIcon:b.any,maxTagCount:b.oneOfType([b.number,b.string]),maxTagTextLength:Number,maxTagPlaceholder:b.any,tagRender:Function,optionLabelRender:Function,tokenWithEnter:{type:Boolean,default:void 0},choiceTransitionName:String,onToggleOpen:{type:Function},onSearch:Function,onSearchSubmit:Function,onRemove:Function,onInputKeyDown:{type:Function},domRef:Function},setup(e,t){let{expose:n}=t;const o=Te(),l=pe(!1),[i,c]=gt(0),r=S=>{const{which:w}=S;(w===R.UP||w===R.DOWN)&&S.preventDefault(),e.onInputKeyDown&&e.onInputKeyDown(S),w===R.ENTER&&e.mode==="tags"&&!l.value&&!e.open&&e.onSearchSubmit(S.target.value),Tn(w)&&e.onToggleOpen(!0)},p=()=>{c(!0)};let a=null;const s=S=>{e.onSearch(S,!0,l.value)!==!1&&e.onToggleOpen(!0)},g=()=>{l.value=!0},d=S=>{l.value=!1,e.mode!=="combobox"&&s(S.target.value)},M=S=>{let{target:{value:w}}=S;if(e.tokenWithEnter&&a&&/[\r\n]/.test(a)){const E=a.replace(/[\r\n]+$/,"").replace(/\r\n/g," ").replace(/[\r\n]/g," ");w=w.replace(E,a)}a=null,s(w)},v=S=>{const{clipboardData:w}=S;a=w.getData("text")},$=S=>{let{target:w}=S;w!==o.current&&(document.body.style.msTouchAction!==void 0?setTimeout(()=>{o.current.focus()}):o.current.focus())},I=S=>{const w=i();S.target!==o.current&&!w&&S.preventDefault(),(e.mode!=="combobox"&&(!e.showSearch||!w)||!e.open)&&(e.open&&e.onSearch("",!0,!1),e.onToggleOpen())};return n({focus:()=>{o.current.focus()},blur:()=>{o.current.blur()}}),()=>{const{prefixCls:S,domRef:w,mode:E}=e,x={inputRef:o,onInputKeyDown:r,onInputMouseDown:p,onInputChange:M,onInputPaste:v,compositionStatus:l.value,onInputCompositionStart:g,onInputCompositionEnd:d},D=E==="multiple"||E==="tags"?y(Cn,ee(ee({},e),x),null):y(Ye,ee(ee({},e),x),null);return y("div",{ref:w,class:`${S}-selector`,onClick:$,onMousedown:I},[D])}}});function En(e,t,n){function o(l){var i,c,r;let p=l.target;p.shadowRoot&&l.composed&&(p=l.composedPath()[0]||p);const a=[(i=e[0])===null||i===void 0?void 0:i.value,(r=(c=e[1])===null||c===void 0?void 0:c.value)===null||r===void 0?void 0:r.getPopupElement()];t.value&&a.every(s=>s&&!s.contains(p)&&s!==p)&&n(!1)}ae(()=>{window.addEventListener("mousedown",o)}),ye(()=>{window.removeEventListener("mousedown",o)})}function Rn(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:10;const t=A(!1);let n;const o=()=>{clearTimeout(n)};return ae(()=>{o()}),[t,(i,c)=>{o(),n=setTimeout(()=>{t.value=i,c&&c()},e)},o]}const ht=Symbol("BaseSelectContextKey");function Fn(e){return Ke(ht,e)}function Po(){return We(ht,{})}function Pn(e){if(!en(e))return be(e);const t=new Proxy({},{get(n,o,l){return Reflect.get(e.value,o,l)},set(n,o,l){return e.value[o]=l,!0},deleteProperty(n,o){return Reflect.deleteProperty(e.value,o)},has(n,o){return Reflect.has(e.value,o)},ownKeys(){return Object.keys(e.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return be(t)}var Dn=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,o=Object.getOwnPropertySymbols(e);l<o.length;l++)t.indexOf(o[l])<0&&Object.prototype.propertyIsEnumerable.call(e,o[l])&&(n[o[l]]=e[o[l]]);return n};const On=["value","onChange","removeIcon","placeholder","autofocus","maxTagCount","maxTagTextLength","maxTagPlaceholder","choiceTransitionName","onInputKeyDown","onPopupScroll","tabindex","OptionList","notFoundContent"],Hn=()=>({prefixCls:String,id:String,omitDomProps:Array,displayValues:Array,onDisplayValuesChange:Function,activeValue:String,activeDescendantId:String,onActiveValueChange:Function,searchValue:String,onSearch:Function,onSearchSplit:Function,maxLength:Number,OptionList:b.any,emptyOptions:Boolean}),Bn=()=>({showSearch:{type:Boolean,default:void 0},tagRender:{type:Function},optionLabelRender:{type:Function},direction:{type:String},tabindex:Number,autofocus:Boolean,notFoundContent:b.any,placeholder:b.any,onClear:Function,choiceTransitionName:String,mode:String,disabled:{type:Boolean,default:void 0},loading:{type:Boolean,default:void 0},open:{type:Boolean,default:void 0},defaultOpen:{type:Boolean,default:void 0},onDropdownVisibleChange:{type:Function},getInputElement:{type:Function},getRawInputElement:{type:Function},maxTagTextLength:Number,maxTagCount:{type:[String,Number]},maxTagPlaceholder:b.any,tokenSeparators:{type:Array},allowClear:{type:Boolean,default:void 0},showArrow:{type:Boolean,default:void 0},inputIcon:b.any,clearIcon:b.any,removeIcon:b.any,animation:String,transitionName:String,dropdownStyle:{type:Object},dropdownClassName:String,dropdownMatchSelectWidth:{type:[Boolean,Number],default:void 0},dropdownRender:{type:Function},dropdownAlign:Object,placement:{type:String},getPopupContainer:{type:Function},showAction:{type:Array},onBlur:{type:Function},onFocus:{type:Function},onKeyup:Function,onKeydown:Function,onMousedown:Function,onPopupScroll:Function,onInputKeyDown:Function,onMouseenter:Function,onMouseleave:Function,onClick:Function}),Ln=()=>h(h({},Hn()),Bn());function Nn(e){return e==="tags"||e==="multiple"}const Do=re({compatConfig:{MODE:3},name:"BaseSelect",inheritAttrs:!1,props:_t(Ln(),{showAction:[],notFoundContent:"Not Found"}),setup(e,t){let{attrs:n,expose:o,slots:l}=t;const i=z(()=>Nn(e.mode)),c=z(()=>e.showSearch!==void 0?e.showSearch:i.value||e.mode==="combobox"),r=A(!1);ae(()=>{r.value=sn()});const p=je(),a=A(null),s=Te(),g=A(null),d=A(null),M=A(null),v=pe(!1),[$,I,S]=Rn();o({focus:()=>{var f;(f=d.value)===null||f===void 0||f.focus()},blur:()=>{var f;(f=d.value)===null||f===void 0||f.blur()},scrollTo:f=>{var m;return(m=M.value)===null||m===void 0?void 0:m.scrollTo(f)}});const x=z(()=>{var f;if(e.mode!=="combobox")return e.searchValue;const m=(f=e.displayValues[0])===null||f===void 0?void 0:f.value;return typeof m=="string"||typeof m=="number"?String(m):""}),D=e.open!==void 0?e.open:e.defaultOpen,F=A(D),T=A(D),V=f=>{F.value=e.open!==void 0?e.open:f,T.value=F.value};G(()=>e.open,()=>{V(e.open)});const O=z(()=>!e.notFoundContent&&e.emptyOptions);_e(()=>{T.value=F.value,(e.disabled||O.value&&T.value&&e.mode==="combobox")&&(T.value=!1)});const L=z(()=>O.value?!1:T.value),P=f=>{const m=f!==void 0?f:!T.value;T.value!==m&&!e.disabled&&(V(m),e.onDropdownVisibleChange&&e.onDropdownVisibleChange(m),!m&&U.value&&(U.value=!1,I(!1,()=>{C.value=!1,v.value=!1})))},B=z(()=>(e.tokenSeparators||[]).some(f=>[`
`,`\r
`].includes(f))),W=(f,m,_)=>{var Y,Z;let X=!0,q=f;(Y=e.onActiveValueChange)===null||Y===void 0||Y.call(e,null);const Q=_?null:Sn(f,e.tokenSeparators);return e.mode!=="combobox"&&Q&&(q="",(Z=e.onSearchSplit)===null||Z===void 0||Z.call(e,Q),P(!1),X=!1),e.onSearch&&x.value!==q&&e.onSearch(q,{source:m?"typing":"effect"}),X},j=f=>{var m;!f||!f.trim()||(m=e.onSearch)===null||m===void 0||m.call(e,f,{source:"submit"})};G(T,()=>{!T.value&&!i.value&&e.mode!=="combobox"&&W("",!1,!1)},{immediate:!0,flush:"post"}),G(()=>e.disabled,()=>{F.value&&e.disabled&&V(!1),e.disabled&&!v.value&&I(!1)},{immediate:!0});const[H,K]=gt(),oe=function(f){var m;const _=H(),{which:Y}=f;if(Y===R.ENTER&&(e.mode!=="combobox"&&f.preventDefault(),T.value||P(!0)),K(!!x.value),Y===R.BACKSPACE&&!_&&i.value&&!x.value&&e.displayValues.length){const Q=[...e.displayValues];let ce=null;for(let k=Q.length-1;k>=0;k-=1){const de=Q[k];if(!de.disabled){Q.splice(k,1),ce=de;break}}ce&&e.onDisplayValuesChange(Q,{type:"remove",values:[ce]})}for(var Z=arguments.length,X=new Array(Z>1?Z-1:0),q=1;q<Z;q++)X[q-1]=arguments[q];T.value&&M.value&&M.value.onKeydown(f,...X),(m=e.onKeydown)===null||m===void 0||m.call(e,f,...X)},se=function(f){for(var m=arguments.length,_=new Array(m>1?m-1:0),Y=1;Y<m;Y++)_[Y-1]=arguments[Y];T.value&&M.value&&M.value.onKeyup(f,..._),e.onKeyup&&e.onKeyup(f,..._)},u=f=>{const m=e.displayValues.filter(_=>_!==f);e.onDisplayValuesChange(m,{type:"remove",values:[f]})},C=A(!1),N=function(){I(!0),e.disabled||(e.onFocus&&!C.value&&e.onFocus(...arguments),e.showAction&&e.showAction.includes("focus")&&P(!0)),C.value=!0},U=pe(!1),he=function(){if(U.value||(v.value=!0,I(!1,()=>{C.value=!1,v.value=!1,P(!1)}),e.disabled))return;const f=x.value;f&&(e.mode==="tags"?e.onSearch(f,{source:"submit"}):e.mode==="multiple"&&e.onSearch("",{source:"blur"})),e.onBlur&&e.onBlur(...arguments)},Ee=()=>{U.value=!0},we=()=>{U.value=!1};Ke("VCSelectContainerEvent",{focus:N,blur:he});const J=[];ae(()=>{J.forEach(f=>clearTimeout(f)),J.splice(0,J.length)}),ye(()=>{J.forEach(f=>clearTimeout(f)),J.splice(0,J.length)});const xe=function(f){var m,_;const{target:Y}=f,Z=(m=g.value)===null||m===void 0?void 0:m.getPopupElement();if(Z&&Z.contains(Y)){const ce=setTimeout(()=>{var k;const de=J.indexOf(ce);de!==-1&&J.splice(de,1),S(),!r.value&&!Z.contains(document.activeElement)&&((k=d.value)===null||k===void 0||k.focus())});J.push(ce)}for(var X=arguments.length,q=new Array(X>1?X-1:0),Q=1;Q<X;Q++)q[Q-1]=arguments[Q];(_=e.onMousedown)===null||_===void 0||_.call(e,f,...q)},ve=A(null),ue=()=>{};return ae(()=>{G(L,()=>{var f;if(L.value){const m=Math.ceil((f=a.value)===null||f===void 0?void 0:f.offsetWidth);ve.value!==m&&!Number.isNaN(m)&&(ve.value=m)}},{immediate:!0,flush:"post"})}),En([a,g],L,P),Fn(Pn(h(h({},tn(e)),{open:T,triggerOpen:L,showSearch:c,multiple:i,toggleOpen:P}))),()=>{const f=h(h({},e),n),{prefixCls:m,id:_,open:Y,defaultOpen:Z,mode:X,showSearch:q,searchValue:Q,onSearch:ce,allowClear:k,clearIcon:de,showArrow:Ue,inputIcon:yt,disabled:Re,loading:Ie,getInputElement:Xe,getPopupContainer:wt,placement:xt,animation:It,transitionName:Ct,dropdownStyle:$t,dropdownClassName:Tt,dropdownMatchSelectWidth:Mt,dropdownRender:Et,dropdownAlign:Rt,showAction:io,direction:Ft,tokenSeparators:ao,tagRender:Pt,optionLabelRender:Dt,onPopupScroll:ro,onDropdownVisibleChange:so,onFocus:uo,onBlur:co,onKeyup:fo,onKeydown:po,onMousedown:mo,onClear:Fe,omitDomProps:Pe,getRawInputElement:Ge,displayValues:Ce,onDisplayValuesChange:Ot,emptyOptions:Ht,activeDescendantId:Bt,activeValue:Lt,OptionList:Nt}=f,zt=Dn(f,["prefixCls","id","open","defaultOpen","mode","showSearch","searchValue","onSearch","allowClear","clearIcon","showArrow","inputIcon","disabled","loading","getInputElement","getPopupContainer","placement","animation","transitionName","dropdownStyle","dropdownClassName","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","showAction","direction","tokenSeparators","tagRender","optionLabelRender","onPopupScroll","onDropdownVisibleChange","onFocus","onBlur","onKeyup","onKeydown","onMousedown","onClear","omitDomProps","getRawInputElement","displayValues","onDisplayValuesChange","emptyOptions","activeDescendantId","activeValue","OptionList"]),Qe=X==="combobox"&&Xe&&Xe()||null,Se=typeof Ge=="function"&&Ge(),De=h({},zt);let Je;Se&&(Je=fe=>{P(fe)}),On.forEach(fe=>{delete De[fe]}),Pe==null||Pe.forEach(fe=>{delete De[fe]});const Ze=Ue!==void 0?Ue:Ie||!i.value&&X!=="combobox";let qe;Ze&&(qe=y(ge,{class:ne(`${m}-arrow`,{[`${m}-arrow-loading`]:Ie}),customizeIcon:yt,customizeIconProps:{loading:Ie,searchValue:x.value,open:T.value,focused:$.value,showSearch:c.value}},null));let ke;const Vt=()=>{Fe==null||Fe(),Ot([],{type:"clear",values:Ce}),W("",!1,!1)};!Re&&k&&(Ce.length||x.value)&&(ke=y(ge,{class:`${m}-clear`,onMousedown:Vt,customizeIcon:de},{default:()=>[Ve("×")]}));const At=y(Nt,{ref:M},h(h({},p.customSlots),{option:l.option})),Wt=ne(m,n.class,{[`${m}-focused`]:$.value,[`${m}-multiple`]:i.value,[`${m}-single`]:!i.value,[`${m}-allow-clear`]:k,[`${m}-show-arrow`]:Ze,[`${m}-disabled`]:Re,[`${m}-loading`]:Ie,[`${m}-open`]:T.value,[`${m}-customize-input`]:Qe,[`${m}-show-search`]:c.value}),et=y(wn,{ref:g,disabled:Re,prefixCls:m,visible:L.value,popupElement:At,containerWidth:ve.value,animation:It,transitionName:Ct,dropdownStyle:$t,dropdownClassName:Tt,direction:Ft,dropdownMatchSelectWidth:Mt,dropdownRender:Et,dropdownAlign:Rt,placement:xt,getPopupContainer:wt,empty:Ht,getTriggerDOMNode:()=>s.current,onPopupVisibleChange:Je,onPopupMouseEnter:ue,onPopupFocusin:Ee,onPopupFocusout:we},{default:()=>Se?jt(Se)&&ft(Se,{ref:s},!1,!0):y(Mn,ee(ee({},e),{},{domRef:s,prefixCls:m,inputElement:Qe,ref:d,id:_,showSearch:c.value,mode:X,activeDescendantId:Bt,tagRender:Pt,optionLabelRender:Dt,values:Ce,open:T.value,onToggleOpen:P,activeValue:Lt,searchValue:x.value,onSearch:W,onSearchSubmit:j,onRemove:u,tokenWithEnter:B.value}),null)});let Oe;return Se?Oe=et:Oe=y("div",ee(ee({},De),{},{class:Wt,ref:a,onMousedown:xe,onKeydown:oe,onKeyup:se}),[$.value&&!T.value&&y("span",{style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0},"aria-live":"polite"},[`${Ce.map(fe=>{let{label:tt,value:Kt}=fe;return["number","string"].includes(typeof tt)?tt:Kt}).join(", ")}`]),et,qe,ke]),Oe}}}),Me=(e,t)=>{let{height:n,offset:o,prefixCls:l,onInnerResize:i}=e,{slots:c}=t;var r;let p={},a={display:"flex",flexDirection:"column"};return o!==void 0&&(p={height:`${n}px`,position:"relative",overflow:"hidden"},a=h(h({},a),{transform:`translateY(${o}px)`,position:"absolute",left:0,right:0,top:0})),y("div",{style:p},[y(un,{onResize:s=>{let{offsetHeight:g}=s;g&&i&&i()}},{default:()=>[y("div",{style:a,class:ne({[`${l}-holder-inner`]:l})},[(r=c.default)===null||r===void 0?void 0:r.call(c)])]})])};Me.displayName="Filter";Me.inheritAttrs=!1;Me.props={prefixCls:String,height:Number,offset:Number,onInnerResize:Function};const vt=(e,t)=>{let{setRef:n}=e,{slots:o}=t;var l;const i=Yt((l=o.default)===null||l===void 0?void 0:l.call(o));return i&&i.length?dt(i[0],{ref:n}):i};vt.props={setRef:{type:Function,default:()=>{}}};const zn=20;function at(e){return"touches"in e?e.touches[0].pageY:e.pageY}const Vn=re({compatConfig:{MODE:3},name:"ScrollBar",inheritAttrs:!1,props:{prefixCls:String,scrollTop:Number,scrollHeight:Number,height:Number,count:Number,onScroll:{type:Function},onStartMove:{type:Function},onStopMove:{type:Function}},setup(){return{moveRaf:null,scrollbarRef:Te(),thumbRef:Te(),visibleTimeout:null,state:be({dragging:!1,pageY:null,startTop:null,visible:!1})}},watch:{scrollTop:{handler(){this.delayHidden()},flush:"post"}},mounted(){var e,t;(e=this.scrollbarRef.current)===null||e===void 0||e.addEventListener("touchstart",this.onScrollbarTouchStart,le?{passive:!1}:!1),(t=this.thumbRef.current)===null||t===void 0||t.addEventListener("touchstart",this.onMouseDown,le?{passive:!1}:!1)},beforeUnmount(){this.removeEvents(),clearTimeout(this.visibleTimeout)},methods:{delayHidden(){clearTimeout(this.visibleTimeout),this.state.visible=!0,this.visibleTimeout=setTimeout(()=>{this.state.visible=!1},2e3)},onScrollbarTouchStart(e){e.preventDefault()},onContainerMouseDown(e){e.stopPropagation(),e.preventDefault()},patchEvents(){window.addEventListener("mousemove",this.onMouseMove),window.addEventListener("mouseup",this.onMouseUp),this.thumbRef.current.addEventListener("touchmove",this.onMouseMove,le?{passive:!1}:!1),this.thumbRef.current.addEventListener("touchend",this.onMouseUp)},removeEvents(){window.removeEventListener("mousemove",this.onMouseMove),window.removeEventListener("mouseup",this.onMouseUp),this.scrollbarRef.current.removeEventListener("touchstart",this.onScrollbarTouchStart,le?{passive:!1}:!1),this.thumbRef.current&&(this.thumbRef.current.removeEventListener("touchstart",this.onMouseDown,le?{passive:!1}:!1),this.thumbRef.current.removeEventListener("touchmove",this.onMouseMove,le?{passive:!1}:!1),this.thumbRef.current.removeEventListener("touchend",this.onMouseUp)),te.cancel(this.moveRaf)},onMouseDown(e){const{onStartMove:t}=this.$props;h(this.state,{dragging:!0,pageY:at(e),startTop:this.getTop()}),t(),this.patchEvents(),e.stopPropagation(),e.preventDefault()},onMouseMove(e){const{dragging:t,pageY:n,startTop:o}=this.state,{onScroll:l}=this.$props;if(te.cancel(this.moveRaf),t){const i=at(e)-n,c=o+i,r=this.getEnableScrollRange(),p=this.getEnableHeightRange(),a=p?c/p:0,s=Math.ceil(a*r);this.moveRaf=te(()=>{l(s)})}},onMouseUp(){const{onStopMove:e}=this.$props;this.state.dragging=!1,e(),this.removeEvents()},getSpinHeight(){const{height:e,scrollHeight:t}=this.$props;let n=e/t*100;return n=Math.max(n,zn),n=Math.min(n,e/2),Math.floor(n)},getEnableScrollRange(){const{scrollHeight:e,height:t}=this.$props;return e-t||0},getEnableHeightRange(){const{height:e}=this.$props,t=this.getSpinHeight();return e-t||0},getTop(){const{scrollTop:e}=this.$props,t=this.getEnableScrollRange(),n=this.getEnableHeightRange();return e===0||t===0?0:e/t*n},showScroll(){const{height:e,scrollHeight:t}=this.$props;return t>e}},render(){const{dragging:e,visible:t}=this.state,{prefixCls:n}=this.$props,o=this.getSpinHeight()+"px",l=this.getTop()+"px",i=this.showScroll(),c=i&&t;return y("div",{ref:this.scrollbarRef,class:ne(`${n}-scrollbar`,{[`${n}-scrollbar-show`]:i}),style:{width:"8px",top:0,bottom:0,right:0,position:"absolute",display:c?void 0:"none"},onMousedown:this.onContainerMouseDown,onMousemove:this.delayHidden},[y("div",{ref:this.thumbRef,class:ne(`${n}-scrollbar-thumb`,{[`${n}-scrollbar-thumb-moving`]:e}),style:{width:"100%",height:o,top:l,left:0,position:"absolute",background:"rgba(0, 0, 0, 0.5)",borderRadius:"99px",cursor:"pointer",userSelect:"none"},onMousedown:this.onMouseDown},null)])}});function An(e,t,n,o){const l=new Map,i=new Map,c=pe(Symbol("update"));G(e,()=>{c.value=Symbol("update")});let r;function p(){te.cancel(r)}function a(){p(),r=te(()=>{l.forEach((g,d)=>{if(g&&g.offsetParent){const{offsetHeight:M}=g;i.get(d)!==M&&(c.value=Symbol("update"),i.set(d,g.offsetHeight))}})})}function s(g,d){const M=t(g);l.get(M),d?(l.set(M,d.$el||d),a()):l.delete(M)}return nn(()=>{p()}),[s,a,i,c]}function Wn(e,t,n,o,l,i,c,r){let p;return a=>{if(a==null){r();return}te.cancel(p);const s=t.value,g=o.itemHeight;if(typeof a=="number")c(a);else if(a&&typeof a=="object"){let d;const{align:M}=a;"index"in a?{index:d}=a:d=s.findIndex(I=>l(I)===a.key);const{offset:v=0}=a,$=(I,S)=>{if(I<0||!e.value)return;const w=e.value.clientHeight;let E=!1,x=S;if(w){const D=S||M;let F=0,T=0,V=0;const O=Math.min(s.length,d);for(let B=0;B<=O;B+=1){const W=l(s[B]);T=F;const j=n.get(W);V=T+(j===void 0?g:j),F=V,B===d&&j===void 0&&(E=!0)}const L=e.value.scrollTop;let P=null;switch(D){case"top":P=T-v;break;case"bottom":P=V-w+v;break;default:{const B=L+w;T<L?x="top":V>B&&(x="bottom")}}P!==null&&P!==L&&c(P)}p=te(()=>{E&&i(),$(I-1,x)},2)};$(5)}}}const Kn=typeof navigator=="object"&&/Firefox/i.test(navigator.userAgent),St=(e,t)=>{let n=!1,o=null;function l(){clearTimeout(o),n=!0,o=setTimeout(()=>{n=!1},50)}return function(i){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const r=i<0&&e.value||i>0&&t.value;return c&&r?(clearTimeout(o),n=!1):(!r||n)&&l(),!n&&r}};function _n(e,t,n,o){let l=0,i=null,c=null,r=!1;const p=St(t,n);function a(g){if(!e.value)return;te.cancel(i);const{deltaY:d}=g;l+=d,c=d,!p(d)&&(Kn||g.preventDefault(),i=te(()=>{o(l*(r?10:1)),l=0}))}function s(g){e.value&&(r=g.detail===c)}return[a,s]}const jn=14/15;function Yn(e,t,n){let o=!1,l=0,i=null,c=null;const r=()=>{i&&(i.removeEventListener("touchmove",p),i.removeEventListener("touchend",a))},p=d=>{if(o){const M=Math.ceil(d.touches[0].pageY);let v=l-M;l=M,n(v)&&d.preventDefault(),clearInterval(c),c=setInterval(()=>{v*=jn,(!n(v,!0)||Math.abs(v)<=.1)&&clearInterval(c)},16)}},a=()=>{o=!1,r()},s=d=>{r(),d.touches.length===1&&!o&&(o=!0,l=Math.ceil(d.touches[0].pageY),i=d.target,i.addEventListener("touchmove",p,{passive:!1}),i.addEventListener("touchend",a))},g=()=>{};ae(()=>{document.addEventListener("touchmove",g,{passive:!1}),G(e,d=>{t.value.removeEventListener("touchstart",s),r(),clearInterval(c),d&&t.value.addEventListener("touchstart",s,{passive:!1})},{immediate:!0})}),ye(()=>{document.removeEventListener("touchmove",g)})}var Un=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,o=Object.getOwnPropertySymbols(e);l<o.length;l++)t.indexOf(o[l])<0&&Object.prototype.propertyIsEnumerable.call(e,o[l])&&(n[o[l]]=e[o[l]]);return n};const Xn=[],Gn={overflowY:"auto",overflowAnchor:"none"};function Qn(e,t,n,o,l,i){let{getKey:c}=i;return e.slice(t,n+1).map((r,p)=>{const a=t+p,s=l(r,a,{}),g=c(r);return y(vt,{key:g,setRef:d=>o(r,d)},{default:()=>[s]})})}const Oo=re({compatConfig:{MODE:3},name:"List",inheritAttrs:!1,props:{prefixCls:String,data:b.array,height:Number,itemHeight:Number,fullHeight:{type:Boolean,default:void 0},itemKey:{type:[String,Number,Function],required:!0},component:{type:[String,Object]},virtual:{type:Boolean,default:void 0},children:Function,onScroll:Function,onMousedown:Function,onMouseenter:Function,onVisibleChange:Function},setup(e,t){let{expose:n}=t;const o=z(()=>{const{height:u,itemHeight:C,virtual:N}=e;return!!(N!==!1&&u&&C)}),l=z(()=>{const{height:u,itemHeight:C,data:N}=e;return o.value&&N&&C*N.length>u}),i=be({scrollTop:0,scrollMoving:!1}),c=z(()=>e.data||Xn),r=A([]);G(c,()=>{r.value=ln(c.value).slice()},{immediate:!0});const p=A(u=>{});G(()=>e.itemKey,u=>{typeof u=="function"?p.value=u:p.value=C=>C==null?void 0:C[u]},{immediate:!0});const a=A(),s=A(),g=A(),d=u=>p.value(u),M={getKey:d};function v(u){let C;typeof u=="function"?C=u(i.scrollTop):C=u;const N=F(C);a.value&&(a.value.scrollTop=N),i.scrollTop=N}const[$,I,S,w]=An(r,d),E=be({scrollHeight:void 0,start:0,end:0,offset:void 0}),x=A(0);ae(()=>{He(()=>{var u;x.value=((u=s.value)===null||u===void 0?void 0:u.offsetHeight)||0})}),on(()=>{He(()=>{var u;x.value=((u=s.value)===null||u===void 0?void 0:u.offsetHeight)||0})}),G([o,r],()=>{o.value||h(E,{scrollHeight:void 0,start:0,end:r.value.length-1,offset:void 0})},{immediate:!0}),G([o,r,x,l],()=>{o.value&&!l.value&&h(E,{scrollHeight:x.value,start:0,end:r.value.length-1,offset:void 0}),a.value&&(i.scrollTop=a.value.scrollTop)},{immediate:!0}),G([l,o,()=>i.scrollTop,r,w,()=>e.height,x],()=>{if(!o.value||!l.value)return;let u=0,C,N,U;const he=r.value.length,Ee=r.value,we=i.scrollTop,{itemHeight:J,height:xe}=e,ve=we+xe;for(let ue=0;ue<he;ue+=1){const f=Ee[ue],m=d(f);let _=S.get(m);_===void 0&&(_=J);const Y=u+_;C===void 0&&Y>=we&&(C=ue,N=u),U===void 0&&Y>ve&&(U=ue),u=Y}C===void 0&&(C=0,N=0,U=Math.ceil(xe/J)),U===void 0&&(U=he-1),U=Math.min(U+1,he),h(E,{scrollHeight:u,start:C,end:U,offset:N})},{immediate:!0});const D=z(()=>E.scrollHeight-e.height);function F(u){let C=u;return Number.isNaN(D.value)||(C=Math.min(C,D.value)),C=Math.max(C,0),C}const T=z(()=>i.scrollTop<=0),V=z(()=>i.scrollTop>=D.value),O=St(T,V);function L(u){v(u)}function P(u){var C;const{scrollTop:N}=u.currentTarget;N!==i.scrollTop&&v(N),(C=e.onScroll)===null||C===void 0||C.call(e,u)}const[B,W]=_n(o,T,V,u=>{v(C=>C+u)});Yn(o,a,(u,C)=>O(u,C)?!1:(B({preventDefault(){},deltaY:u}),!0));function j(u){o.value&&u.preventDefault()}const H=()=>{a.value&&(a.value.removeEventListener("wheel",B,le?{passive:!1}:!1),a.value.removeEventListener("DOMMouseScroll",W),a.value.removeEventListener("MozMousePixelScroll",j))};_e(()=>{He(()=>{a.value&&(H(),a.value.addEventListener("wheel",B,le?{passive:!1}:!1),a.value.addEventListener("DOMMouseScroll",W),a.value.addEventListener("MozMousePixelScroll",j))})}),ye(()=>{H()});const K=Wn(a,r,S,e,d,I,v,()=>{var u;(u=g.value)===null||u===void 0||u.delayHidden()});n({scrollTo:K});const oe=z(()=>{let u=null;return e.height&&(u=h({[e.fullHeight?"height":"maxHeight"]:e.height+"px"},Gn),o.value&&(u.overflowY="hidden",i.scrollMoving&&(u.pointerEvents="none"))),u});return G([()=>E.start,()=>E.end,r],()=>{if(e.onVisibleChange){const u=r.value.slice(E.start,E.end+1);e.onVisibleChange(u,r.value)}},{flush:"post"}),{state:i,mergedData:r,componentStyle:oe,onFallbackScroll:P,onScrollBar:L,componentRef:a,useVirtual:o,calRes:E,collectHeight:I,setInstance:$,sharedConfig:M,scrollBarRef:g,fillerInnerRef:s,delayHideScrollBar:()=>{var u;(u=g.value)===null||u===void 0||u.delayHidden()}}},render(){const e=h(h({},this.$props),this.$attrs),{prefixCls:t="rc-virtual-list",height:n,itemHeight:o,fullHeight:l,data:i,itemKey:c,virtual:r,component:p="div",onScroll:a,children:s=this.$slots.default,style:g,class:d}=e,M=Un(e,["prefixCls","height","itemHeight","fullHeight","data","itemKey","virtual","component","onScroll","children","style","class"]),v=ne(t,d),{scrollTop:$}=this.state,{scrollHeight:I,offset:S,start:w,end:E}=this.calRes,{componentStyle:x,onFallbackScroll:D,onScrollBar:F,useVirtual:T,collectHeight:V,sharedConfig:O,setInstance:L,mergedData:P,delayHideScrollBar:B}=this;return y("div",ee({style:h(h({},g),{position:"relative"}),class:v},M),[y(p,{class:`${t}-holder`,style:x,ref:"componentRef",onScroll:D,onMouseenter:B},{default:()=>[y(Me,{prefixCls:t,height:I,offset:S,onInnerResize:V,ref:"fillerInnerRef"},{default:()=>Qn(P,w,E,L,s,O)})]}),T&&y(Vn,{ref:"scrollBarRef",prefixCls:t,scrollTop:$,height:n,scrollHeight:I,count:P.length,onScroll:F,onStartMove:()=>{this.state.scrollMoving=!0},onStopMove:()=>{this.state.scrollMoving=!1}},null)])}});let rt=0;const Jn=Ut();function Zn(){let e;return Jn?(e=rt,rt+=1):e="TEST_OR_SSR",e}function Ho(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:pe("");const t=`rc_select_${Zn()}`;return e.value||t}function Bo(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{loading:n,multiple:o,prefixCls:l,hasFeedback:i,feedbackIcon:c,showArrow:r}=e,p=e.suffixIcon||t.suffixIcon&&t.suffixIcon(),a=e.clearIcon||t.clearIcon&&t.clearIcon(),s=e.menuItemSelectedIcon||t.menuItemSelectedIcon&&t.menuItemSelectedIcon(),g=e.removeIcon||t.removeIcon&&t.removeIcon(),d=a!=null?a:y(Xt,null,null),M=S=>y($e,null,[r!==!1&&S,i&&c]);let v=null;if(p!==void 0)v=M(p);else if(n)v=M(y(Gt,{spin:!0},null));else{const S=`${l}-suffix`;v=w=>{let{open:E,showSearch:x}=w;return M(E&&x?y(fn,{class:S},null):y(cn,{class:S},null))}}let $=null;s!==void 0?$=s:o?$=y(dn,null,null):$=null;let I=null;return g!==void 0?I=g:I=y(Qt,null,null),{clearIcon:d,suffixIcon:v,itemIcon:$,removeIcon:I}}const st=e=>{const{controlPaddingHorizontal:t}=e;return{position:"relative",display:"block",minHeight:e.controlHeight,padding:`${(e.controlHeight-e.fontSize*e.lineHeight)/2}px ${t}px`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,boxSizing:"border-box"}},qn=e=>{const{antCls:t,componentCls:n}=e,o=`${n}-item`;return[{[`${n}-dropdown`]:h(h({},Ae(e)),{position:"absolute",top:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",padding:e.paddingXXS,overflow:"hidden",fontSize:e.fontSize,fontVariant:"initial",backgroundColor:e.colorBgElevated,borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,[`
            &${t}-slide-up-enter${t}-slide-up-enter-active${n}-dropdown-placement-bottomLeft,
            &${t}-slide-up-appear${t}-slide-up-appear-active${n}-dropdown-placement-bottomLeft
          `]:{animationName:hn},[`
            &${t}-slide-up-enter${t}-slide-up-enter-active${n}-dropdown-placement-topLeft,
            &${t}-slide-up-appear${t}-slide-up-appear-active${n}-dropdown-placement-topLeft
          `]:{animationName:gn},[`&${t}-slide-up-leave${t}-slide-up-leave-active${n}-dropdown-placement-bottomLeft`]:{animationName:mn},[`&${t}-slide-up-leave${t}-slide-up-leave-active${n}-dropdown-placement-topLeft`]:{animationName:pn},"&-hidden":{display:"none"},"&-empty":{color:e.colorTextDisabled},[`${o}-empty`]:h(h({},st(e)),{color:e.colorTextDisabled}),[`${o}`]:h(h({},st(e)),{cursor:"pointer",transition:`background ${e.motionDurationSlow} ease`,borderRadius:e.borderRadiusSM,"&-group":{color:e.colorTextDescription,fontSize:e.fontSizeSM,cursor:"default"},"&-option":{display:"flex","&-content":h({flex:"auto"},ze),"&-state":{flex:"none"},[`&-active:not(${o}-option-disabled)`]:{backgroundColor:e.controlItemBgHover},[`&-selected:not(${o}-option-disabled)`]:{color:e.colorText,fontWeight:e.fontWeightStrong,backgroundColor:e.controlItemBgActive,[`${o}-option-state`]:{color:e.colorPrimary}},"&-disabled":{[`&${o}-option-selected`]:{backgroundColor:e.colorBgContainerDisabled},color:e.colorTextDisabled,cursor:"not-allowed"},"&-grouped":{paddingInlineStart:e.controlPaddingHorizontal*2}}}),"&-rtl":{direction:"rtl"}})},ot(e,"slide-up"),ot(e,"slide-down"),nt(e,"move-up"),nt(e,"move-down")]},me=2;function bt(e){let{controlHeightSM:t,controlHeight:n,lineWidth:o}=e;const l=(n-t)/2-o,i=Math.ceil(l/2);return[l,i]}function Be(e,t){const{componentCls:n,iconCls:o}=e,l=`${n}-selection-overflow`,i=e.controlHeightSM,[c]=bt(e),r=t?`${n}-${t}`:"";return{[`${n}-multiple${r}`]:{fontSize:e.fontSize,[l]:{position:"relative",display:"flex",flex:"auto",flexWrap:"wrap",maxWidth:"100%","&-item":{flex:"none",alignSelf:"center",maxWidth:"100%",display:"inline-flex"}},[`${n}-selector`]:{display:"flex",flexWrap:"wrap",alignItems:"center",padding:`${c-me}px ${me*2}px`,borderRadius:e.borderRadius,[`${n}-show-search&`]:{cursor:"text"},[`${n}-disabled&`]:{background:e.colorBgContainerDisabled,cursor:"not-allowed"},"&:after":{display:"inline-block",width:0,margin:`${me}px 0`,lineHeight:`${i}px`,content:'"\\a0"'}},[`
        &${n}-show-arrow ${n}-selector,
        &${n}-allow-clear ${n}-selector
      `]:{paddingInlineEnd:e.fontSizeIcon+e.controlPaddingHorizontal},[`${n}-selection-item`]:{position:"relative",display:"flex",flex:"none",boxSizing:"border-box",maxWidth:"100%",height:i,marginTop:me,marginBottom:me,lineHeight:`${i-e.lineWidth*2}px`,background:e.colorFillSecondary,border:`${e.lineWidth}px solid ${e.colorSplit}`,borderRadius:e.borderRadiusSM,cursor:"default",transition:`font-size ${e.motionDurationSlow}, line-height ${e.motionDurationSlow}, height ${e.motionDurationSlow}`,userSelect:"none",marginInlineEnd:me*2,paddingInlineStart:e.paddingXS,paddingInlineEnd:e.paddingXS/2,[`${n}-disabled&`]:{color:e.colorTextDisabled,borderColor:e.colorBorder,cursor:"not-allowed"},"&-content":{display:"inline-block",marginInlineEnd:e.paddingXS/2,overflow:"hidden",whiteSpace:"pre",textOverflow:"ellipsis"},"&-remove":h(h({},ct()),{display:"inline-block",color:e.colorIcon,fontWeight:"bold",fontSize:10,lineHeight:"inherit",cursor:"pointer",[`> ${o}`]:{verticalAlign:"-0.2em"},"&:hover":{color:e.colorIconHover}})},[`${l}-item + ${l}-item`]:{[`${n}-selection-search`]:{marginInlineStart:0}},[`${n}-selection-search`]:{display:"inline-flex",position:"relative",maxWidth:"100%",marginInlineStart:e.inputPaddingHorizontalBase-c,"\n          &-input,\n          &-mirror\n        ":{height:i,fontFamily:e.fontFamily,lineHeight:`${i}px`,transition:`all ${e.motionDurationSlow}`},"&-input":{width:"100%",minWidth:4.1},"&-mirror":{position:"absolute",top:0,insetInlineStart:0,insetInlineEnd:"auto",zIndex:999,whiteSpace:"pre",visibility:"hidden"}},[`${n}-selection-placeholder `]:{position:"absolute",top:"50%",insetInlineStart:e.inputPaddingHorizontalBase,insetInlineEnd:e.inputPaddingHorizontalBase,transform:"translateY(-50%)",transition:`all ${e.motionDurationSlow}`}}}}function kn(e){const{componentCls:t}=e,n=ie(e,{controlHeight:e.controlHeightSM,controlHeightSM:e.controlHeightXS,borderRadius:e.borderRadiusSM,borderRadiusSM:e.borderRadiusXS}),[,o]=bt(e);return[Be(e),Be(n,"sm"),{[`${t}-multiple${t}-sm`]:{[`${t}-selection-placeholder`]:{insetInlineStart:e.controlPaddingHorizontalSM-e.lineWidth,insetInlineEnd:"auto"},[`${t}-selection-search`]:{marginInlineStart:o}}},Be(ie(e,{fontSize:e.fontSizeLG,controlHeight:e.controlHeightLG,controlHeightSM:e.controlHeight,borderRadius:e.borderRadiusLG,borderRadiusSM:e.borderRadius}),"lg")]}function Le(e,t){const{componentCls:n,inputPaddingHorizontalBase:o,borderRadius:l}=e,i=e.controlHeight-e.lineWidth*2,c=Math.ceil(e.fontSize*1.25),r=t?`${n}-${t}`:"";return{[`${n}-single${r}`]:{fontSize:e.fontSize,[`${n}-selector`]:h(h({},Ae(e)),{display:"flex",borderRadius:l,[`${n}-selection-search`]:{position:"absolute",top:0,insetInlineStart:o,insetInlineEnd:o,bottom:0,"&-input":{width:"100%"}},[`
          ${n}-selection-item,
          ${n}-selection-placeholder
        `]:{padding:0,lineHeight:`${i}px`,transition:`all ${e.motionDurationSlow}`,"@supports (-moz-appearance: meterbar)":{lineHeight:`${i}px`}},[`${n}-selection-item`]:{position:"relative",userSelect:"none"},[`${n}-selection-placeholder`]:{transition:"none",pointerEvents:"none"},[["&:after",`${n}-selection-item:after`,`${n}-selection-placeholder:after`].join(",")]:{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'}}),[`
        &${n}-show-arrow ${n}-selection-item,
        &${n}-show-arrow ${n}-selection-placeholder
      `]:{paddingInlineEnd:c},[`&${n}-open ${n}-selection-item`]:{color:e.colorTextPlaceholder},[`&:not(${n}-customize-input)`]:{[`${n}-selector`]:{width:"100%",height:e.controlHeight,padding:`0 ${o}px`,[`${n}-selection-search-input`]:{height:i},"&:after":{lineHeight:`${i}px`}}},[`&${n}-customize-input`]:{[`${n}-selector`]:{"&:after":{display:"none"},[`${n}-selection-search`]:{position:"static",width:"100%"},[`${n}-selection-placeholder`]:{position:"absolute",insetInlineStart:0,insetInlineEnd:0,padding:`0 ${o}px`,"&:after":{display:"none"}}}}}}}function eo(e){const{componentCls:t}=e,n=e.controlPaddingHorizontalSM-e.lineWidth;return[Le(e),Le(ie(e,{controlHeight:e.controlHeightSM,borderRadius:e.borderRadiusSM}),"sm"),{[`${t}-single${t}-sm`]:{[`&:not(${t}-customize-input)`]:{[`${t}-selection-search`]:{insetInlineStart:n,insetInlineEnd:n},[`${t}-selector`]:{padding:`0 ${n}px`},[`&${t}-show-arrow ${t}-selection-search`]:{insetInlineEnd:n+e.fontSize*1.5},[`
            &${t}-show-arrow ${t}-selection-item,
            &${t}-show-arrow ${t}-selection-placeholder
          `]:{paddingInlineEnd:e.fontSize*1.5}}}},Le(ie(e,{controlHeight:e.controlHeightLG,fontSize:e.fontSizeLG,borderRadius:e.borderRadiusLG}),"lg")]}const to=e=>{const{componentCls:t}=e;return{position:"relative",backgroundColor:e.colorBgContainer,border:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`,transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,input:{cursor:"pointer"},[`${t}-show-search&`]:{cursor:"text",input:{cursor:"auto",color:"inherit"}},[`${t}-disabled&`]:{color:e.colorTextDisabled,background:e.colorBgContainerDisabled,cursor:"not-allowed",[`${t}-multiple&`]:{background:e.colorBgContainerDisabled},input:{cursor:"not-allowed"}}}},Ne=function(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;const{componentCls:o,borderHoverColor:l,outlineColor:i,antCls:c}=t,r=n?{[`${o}-selector`]:{borderColor:l}}:{};return{[e]:{[`&:not(${o}-disabled):not(${o}-customize-input):not(${c}-pagination-size-changer)`]:h(h({},r),{[`${o}-focused& ${o}-selector`]:{borderColor:l,boxShadow:`0 0 0 ${t.controlOutlineWidth}px ${i}`,borderInlineEndWidth:`${t.controlLineWidth}px !important`,outline:0},[`&:hover ${o}-selector`]:{borderColor:l,borderInlineEndWidth:`${t.controlLineWidth}px !important`}})}}},no=e=>{const{componentCls:t}=e;return{[`${t}-selection-search-input`]:{margin:0,padding:0,background:"transparent",border:"none",outline:"none",appearance:"none","&::-webkit-search-cancel-button":{display:"none","-webkit-appearance":"none"}}}},oo=e=>{const{componentCls:t,inputPaddingHorizontalBase:n,iconCls:o}=e;return{[t]:h(h({},Ae(e)),{position:"relative",display:"inline-block",cursor:"pointer",[`&:not(${t}-customize-input) ${t}-selector`]:h(h({},to(e)),no(e)),[`${t}-selection-item`]:h({flex:1,fontWeight:"normal"},ze),[`${t}-selection-placeholder`]:h(h({},ze),{flex:1,color:e.colorTextPlaceholder,pointerEvents:"none"}),[`${t}-arrow`]:h(h({},ct()),{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:n,height:e.fontSizeIcon,marginTop:-e.fontSizeIcon/2,color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,lineHeight:1,textAlign:"center",pointerEvents:"none",display:"flex",alignItems:"center",[o]:{verticalAlign:"top",transition:`transform ${e.motionDurationSlow}`,"> svg":{verticalAlign:"top"},[`&:not(${t}-suffix)`]:{pointerEvents:"auto"}},[`${t}-disabled &`]:{cursor:"not-allowed"},"> *:not(:last-child)":{marginInlineEnd:8}}),[`${t}-clear`]:{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:n,zIndex:1,display:"inline-block",width:e.fontSizeIcon,height:e.fontSizeIcon,marginTop:-e.fontSizeIcon/2,color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",background:e.colorBgContainer,cursor:"pointer",opacity:0,transition:`color ${e.motionDurationMid} ease, opacity ${e.motionDurationSlow} ease`,textRendering:"auto","&:before":{display:"block"},"&:hover":{color:e.colorTextTertiary}},"&:hover":{[`${t}-clear`]:{opacity:1}}}),[`${t}-has-feedback`]:{[`${t}-clear`]:{insetInlineEnd:n+e.fontSize+e.paddingXXS}}}},lo=e=>{const{componentCls:t}=e;return[{[t]:{[`&-borderless ${t}-selector`]:{backgroundColor:"transparent !important",borderColor:"transparent !important",boxShadow:"none !important"},[`&${t}-in-form-item`]:{width:"100%"}}},oo(e),eo(e),kn(e),qn(e),{[`${t}-rtl`]:{direction:"rtl"}},Ne(t,ie(e,{borderHoverColor:e.colorPrimaryHover,outlineColor:e.controlOutline})),Ne(`${t}-status-error`,ie(e,{borderHoverColor:e.colorErrorHover,outlineColor:e.colorErrorOutline}),!0),Ne(`${t}-status-warning`,ie(e,{borderHoverColor:e.colorWarningHover,outlineColor:e.colorWarningOutline}),!0),Zt(e,{borderElCls:`${t}-selector`,focusElCls:`${t}-focused`})]},Lo=Jt("Select",(e,t)=>{let{rootPrefixCls:n}=t;const o=ie(e,{rootPrefixCls:n,inputPaddingHorizontalBase:e.paddingSM-1});return[lo(o)]},e=>({zIndexPopup:e.zIndexPopupBase+50}));export{Do as B,Oo as L,ge as T,Ho as a,Nn as b,Te as c,Eo as d,Bn as e,vn as f,Lo as g,Bo as h,Ro as i,je as j,Fo as k,Pn as t,Po as u};
