var y=(t,d,a)=>new Promise((u,r)=>{var h=e=>{try{n(a.next(e))}catch(s){r(s)}},c=e=>{try{n(a.throw(e))}catch(s){r(s)}},n=e=>e.done?u(e.value):Promise.resolve(e.value).then(h,c);n((a=a.apply(t,d)).next())});import{aY as H}from"./bootstrap-CMNRQ0xm.js";import{a4 as k,P as g,by as C,J as R,a9 as T,av as o,ab as l,aq as i,aB as $,aV as m,a7 as p,aW as v,a8 as f,aj as b,aw as B,n as O}from"../jse/index-index-DjeMElj0.js";const S={class:"relative"},A={class:"flex-auto"},E={key:0,class:"mb-2 flex text-lg font-semibold"},N={key:0,class:"text-muted-foreground"},V={key:0},I=k({name:"Page",__name:"page",props:{title:{},description:{},contentClass:{},autoContentHeight:{type:Boolean,default:!1},headerClass:{},footerClass:{},heightOffset:{default:0}},setup(t){const d=g(0),a=g(0),u=g(!1),r=C("headerRef"),h=C("footerRef"),c=R(()=>t.autoContentHeight?{height:`calc(var(${H}) - ${d.value}px - ${typeof t.heightOffset=="number"?`${t.heightOffset}px`:t.heightOffset})`,overflowY:u.value?"auto":"unset"}:{});function n(){return y(this,null,function*(){var e,s;t.autoContentHeight&&(yield O(),d.value=((e=r.value)==null?void 0:e.offsetHeight)||0,a.value=((s=h.value)==null?void 0:s.offsetHeight)||0,setTimeout(()=>{u.value=!0},30))})}return T(()=>{n()}),(e,s)=>(l(),o("div",S,[e.description||e.$slots.description||e.title||e.$slots.title||e.$slots.extra?(l(),o("div",{key:0,ref_key:"headerRef",ref:r,class:m(p(v)("bg-card border-border relative flex items-end border-b px-6 py-4",e.headerClass))},[$("div",A,[f(e.$slots,"title",{},()=>[e.title?(l(),o("div",E,b(e.title),1)):i("",!0)]),f(e.$slots,"description",{},()=>[e.description?(l(),o("p",N,b(e.description),1)):i("",!0)])]),e.$slots.extra?(l(),o("div",V,[f(e.$slots,"extra")])):i("",!0)],2)):i("",!0),$("div",{class:m(p(v)("h-full p-4",e.contentClass)),style:B(c.value)},[f(e.$slots,"default")],6),e.$slots.footer?(l(),o("div",{key:1,ref_key:"footerRef",ref:h,class:m(p(v)("bg-card align-center absolute bottom-0 left-0 right-0 flex px-6 py-4",e.footerClass))},[f(e.$slots,"footer")],2)):i("",!0)]))}});export{I as _};
