import{_ as h,d as S,e as E,g as m,s as ne,P as te,a as H,h as le,w as oe,f as ue,j as R,b as V}from"./bootstrap-CMNRQ0xm.js";import{V as se}from"./Checkbox-ADkDfYpU.js";import{u as J,F as re}from"./FormItemContext-DdkyQObd.js";import{u as X}from"./index-Bzrm2gpZ.js";import{a4 as Y,J as y,ao as ce,az as ie,R as de,a9 as ve,P as I,x as w,Y as K,aF as fe}from"../jse/index-index-DjeMElj0.js";const me=()=>({name:String,prefixCls:String,options:E([]),disabled:Boolean,id:String}),he=()=>h(h({},me()),{defaultValue:E(),value:E(),onChange:S(),"onUpdate:value":S()}),be=()=>({prefixCls:String,defaultChecked:m(),checked:m(),disabled:m(),isGroup:m(),value:te.any,name:String,id:String,indeterminate:m(),type:ne("checkbox"),autofocus:m(),onChange:S(),"onUpdate:checked":S(),onClick:S(),skipGroup:m(!1)}),ge=()=>h(h({},be()),{indeterminate:m(!1)}),q=Symbol("CheckboxGroupContext");var z=function(e,b){var u={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&b.indexOf(t)<0&&(u[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)b.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(u[t[r]]=e[t[r]]);return u};const O=Y({compatConfig:{MODE:3},name:"ACheckbox",inheritAttrs:!1,__ANT_CHECKBOX:!0,props:ge(),setup(e,b){let{emit:u,attrs:t,slots:r,expose:j}=b;const C=J(),M=re.useInject(),{prefixCls:c,direction:g,disabled:_}=H("checkbox",e),$=le(),[i,f]=X(c),l=de(q,void 0),p=Symbol("checkboxUniId"),G=y(()=>(l==null?void 0:l.disabled.value)||_.value);ce(()=>{!e.skipGroup&&l&&l.registerValue(p,e.value)}),ie(()=>{l&&l.cancelValue(p)}),ve(()=>{oe(!!(e.checked!==void 0||l||e.value===void 0))});const A=a=>{const s=a.target.checked;u("update:checked",s),u("change",a),C.onFieldChange()},x=I();return j({focus:()=>{var a;(a=x.value)===null||a===void 0||a.focus()},blur:()=>{var a;(a=x.value)===null||a===void 0||a.blur()}}),()=>{var a;const s=ue((a=r.default)===null||a===void 0?void 0:a.call(r)),{indeterminate:o,skipGroup:d,id:F=C.id.value}=e,T=z(e,["indeterminate","skipGroup","id"]),{onMouseenter:D,onMouseleave:k,onInput:pe,class:Q,style:W}=t,Z=z(t,["onMouseenter","onMouseleave","onInput","class","style"]),v=h(h(h(h({},T),{id:F,prefixCls:c.value}),Z),{disabled:G.value});l&&!d?(v.onChange=function(){for(var B=arguments.length,N=new Array(B),P=0;P<B;P++)N[P]=arguments[P];u("change",...N),l.toggleOption({label:s,value:e.value})},v.name=l.name.value,v.checked=l.mergedValue.value.includes(e.value),v.disabled=G.value||$.value,v.indeterminate=o):v.onChange=A;const ee=R({[`${c.value}-wrapper`]:!0,[`${c.value}-rtl`]:g.value==="rtl",[`${c.value}-wrapper-checked`]:v.checked,[`${c.value}-wrapper-disabled`]:v.disabled,[`${c.value}-wrapper-in-form-item`]:M.isFormItemInput},Q,f.value),ae=R({[`${c.value}-indeterminate`]:o},f.value);return i(w("label",{class:ee,style:W,onMouseenter:D,onMouseleave:k},[w(se,V(V({"aria-checked":o?"mixed":void 0},v),{},{class:ae,ref:x}),null),s.length?w("span",null,[s]):null]))}}}),U=Y({compatConfig:{MODE:3},name:"ACheckboxGroup",inheritAttrs:!1,props:he(),setup(e,b){let{slots:u,attrs:t,emit:r,expose:j}=b;const C=J(),{prefixCls:M,direction:c}=H("checkbox",e),g=y(()=>`${M.value}-group`),[_,$]=X(g),i=I((e.value===void 0?e.defaultValue:e.value)||[]);K(()=>e.value,()=>{i.value=e.value||[]});const f=y(()=>e.options.map(n=>typeof n=="string"||typeof n=="number"?{label:n,value:n}:n)),l=I(Symbol()),p=I(new Map),G=n=>{p.value.delete(n),l.value=Symbol()},A=(n,a)=>{p.value.set(n,a),l.value=Symbol()},x=I(new Map);return K(l,()=>{const n=new Map;for(const a of p.value.values())n.set(a,!0);x.value=n}),fe(q,{cancelValue:G,registerValue:A,toggleOption:n=>{const a=i.value.indexOf(n.value),s=[...i.value];a===-1?s.push(n.value):s.splice(a,1),e.value===void 0&&(i.value=s);const o=s.filter(d=>x.value.has(d)).sort((d,F)=>{const T=f.value.findIndex(k=>k.value===d),D=f.value.findIndex(k=>k.value===F);return T-D});r("update:value",o),r("change",o),C.onFieldChange()},mergedValue:i,name:y(()=>e.name),disabled:y(()=>e.disabled)}),j({mergedValue:i}),()=>{var n;const{id:a=C.id.value}=e;let s=null;return f.value&&f.value.length>0&&(s=f.value.map(o=>{var d;return w(O,{prefixCls:M.value,key:o.value.toString(),disabled:"disabled"in o?o.disabled:e.disabled,indeterminate:o.indeterminate,value:o.value,checked:i.value.indexOf(o.value)!==-1,onChange:o.onChange,class:`${g.value}-item`},{default:()=>[u.label!==void 0?(d=u.label)===null||d===void 0?void 0:d.call(u,o):o.label]})})),_(w("div",V(V({},t),{},{class:[g.value,{[`${g.value}-rtl`]:c.value==="rtl"},t.class,$.value],id:a}),[s||((n=u.default)===null||n===void 0?void 0:n.call(u))]))}}});O.Group=U;O.install=function(e){return e.component(O.name,O),e.component(U.name,U),e};export{U as CheckboxGroup,he as checkboxGroupProps,ge as checkboxProps,O as default};
