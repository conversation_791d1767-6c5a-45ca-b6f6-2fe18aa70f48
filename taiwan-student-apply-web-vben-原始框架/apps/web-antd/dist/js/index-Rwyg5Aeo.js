var K=Object.defineProperty;var p=Object.getOwnPropertySymbols;var N=Object.prototype.hasOwnProperty,P=Object.prototype.propertyIsEnumerable;var I=(t,e,s)=>e in t?K(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,j=(t,e)=>{for(var s in e||(e={}))N.call(e,s)&&I(t,s,e[s]);if(p)for(var s of p(e))P.call(e,s)&&I(t,s,e[s]);return t};var E=(t,e)=>{var s={};for(var a in t)N.call(t,a)&&e.indexOf(a)<0&&(s[a]=t[a]);if(t!=null&&p)for(var a of p(t))e.indexOf(a)<0&&P.call(t,a)&&(s[a]=t[a]);return s};import{bs as R,bt as U,bu as X}from"./bootstrap-CMNRQ0xm.js";import{c as g}from"./index-DbVlIpuM.js";import{_ as Z}from"./analytics-trends.vue_vue_type_script_setup_true_lang-CVtXNt2s.js";import{_ as aa}from"./analytics-visits-data.vue_vue_type_script_setup_true_lang-DG_s-WLu.js";import{_ as ea}from"./analytics-visits-sales.vue_vue_type_script_setup_true_lang-BDNLJVhn.js";import{_ as ta}from"./analytics-visits-source.vue_vue_type_script_setup_true_lang-C82BXuu0.js";import{_ as sa}from"./analytics-visits.vue_vue_type_script_setup_true_lang-BtgjGXeH.js";import{_ as la,a as na,b as oa,c as ra,d as $}from"./analysis-chart-card.vue_vue_type_script_setup_true_lang-CqkvCc6n.js";import{a4 as f,av as d,ab as c,aV as ia,a7 as l,aW as D,a8 as V,J as T,aa as v,ac as r,ad as ca,P as z,bO as A,ao as ua,Y as da,a9 as fa,bP as _a,bQ as ma,aw as pa,aj as b,x as n,F as y,aC as w,ai as J,aB as L}from"../jse/index-index-DjeMElj0.js";import{_ as va,a as ba,b as ga}from"./TabsList.vue_vue_type_script_setup_true_lang-CWFpQvFK.js";import"./use-echarts-DPHnX0UI.js";const xa=f({__name:"CardFooter",props:{class:{}},setup(t){const e=t;return(s,a)=>(c(),d("div",{class:ia(l(D)("flex items-center p-6 pt-0",e.class))},[V(s.$slots,"default")],2))}}),ha=f({__name:"TabsTrigger",props:{value:{},disabled:{type:Boolean},asChild:{type:Boolean},as:{},class:{}},setup(t){const e=t,s=T(()=>{const m=e,{class:i}=m;return E(m,["class"])}),a=R(s);return(i,o)=>(c(),v(l(U),ca(l(a),{class:l(D)("ring-offset-background focus-visible:ring-ring data-[state=active]:bg-background data-[state=active]:text-foreground inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow",e.class)}),{default:r(()=>[V(i.$slots,"default")]),_:3},16,["class"]))}}),O=f({name:"CountToAnimator",__name:"count-to-animator",props:{autoplay:{type:Boolean,default:!0},color:{default:""},decimal:{default:"."},decimals:{default:0},duration:{default:1500},endVal:{default:2021},prefix:{default:""},separator:{default:","},startVal:{default:0},suffix:{default:""},transition:{default:"linear"},useEasing:{type:Boolean,default:!0}},emits:["finished","onFinished","onStarted","started"],setup(t,{expose:e,emit:s}){const a=t,i=s,o=z(a.startVal),m=z(!1);let k=A(o);const M=T(()=>W(l(k)));ua(()=>{o.value=a.startVal}),da([()=>a.startVal,()=>a.endVal],()=>{a.autoplay&&B()}),fa(()=>{a.autoplay&&B()});function B(){C(),o.value=a.endVal}function Q(){o.value=a.startVal,C()}function C(){k=A(o,j({disabled:m,duration:a.duration,onFinished:()=>{i("finished"),i("onFinished")},onStarted:()=>{i("started"),i("onStarted")}},a.useEasing?{transition:_a[a.transition]}:{}))}function W(u){if(!u&&u!==0)return"";const{decimal:S,decimals:Y,prefix:q,separator:x,suffix:G}=a;u=Number(u).toFixed(Y),u+="";const h=u.split(".");let _=h[0];const H=h.length>1?S+h[1]:"",F=/(\d+)(\d{3})/;if(x&&!ma(x)&&_)for(;F.test(_);)_=_.replace(F,`$1${x}$2`);return q+_+H+G}return e({reset:Q}),(u,S)=>(c(),d("span",{style:pa({color:u.color})},b(M.value),5))}}),$a=g("svg:download"),ya=g("svg:card"),wa=g("svg:bell"),Va=g("svg:cake"),Ta={class:"card-box w-full px-4 pb-5 pt-3"},ka=f({name:"AnalysisChartsTabs",__name:"analysis-charts-tabs",props:{tabs:{default:()=>[]}},setup(t){const e=t,s=T(()=>{var a,i;return(i=(a=e.tabs)==null?void 0:a[0])==null?void 0:i.value});return(a,i)=>(c(),d("div",Ta,[n(l(va),{"default-value":s.value},{default:r(()=>[n(l(ba),null,{default:r(()=>[(c(!0),d(y,null,w(a.tabs,o=>(c(),v(l(ha),{key:o.label,value:o.value},{default:r(()=>[J(b(o.label),1)]),_:2},1032,["value"]))),128))]),_:1}),(c(!0),d(y,null,w(a.tabs,o=>(c(),v(l(ga),{key:o.label,value:o.value,class:"pt-4"},{default:r(()=>[V(a.$slots,o.value)]),_:2},1032,["value"]))),128))]),_:3},8,["default-value"])]))}}),Ba={class:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4"},Ca=f({name:"AnalysisOverview",__name:"analysis-overview",props:{items:{default:()=>[]}},setup(t){return(e,s)=>(c(),d("div",Ba,[(c(!0),d(y,null,w(e.items,a=>(c(),v(l(ra),{key:a.title,title:a.title,class:"w-full"},{default:r(()=>[n(l(la),null,{default:r(()=>[n(l(na),{class:"text-xl"},{default:r(()=>[J(b(a.title),1)]),_:2},1024)]),_:2},1024),n(l(oa),{class:"flex items-center justify-between"},{default:r(()=>[n(l(O),{"end-val":a.value,"start-val":1,class:"text-xl",prefix:""},null,8,["end-val"]),n(l(X),{icon:a.icon,class:"size-8 flex-shrink-0"},null,8,["icon"])]),_:2},1024),n(l(xa),{class:"justify-between"},{default:r(()=>[L("span",null,b(a.totalTitle),1),n(l(O),{"end-val":a.totalValue,"start-val":1,prefix:""},null,8,["end-val"])]),_:2},1024)]),_:2},1032,["title"]))),128))]))}}),Sa={class:"p-5"},Fa={class:"mt-5 w-full md:flex"},Qa=f({__name:"index",setup(t){const e=[{icon:ya,title:"用户量",totalTitle:"总用户量",totalValue:12e4,value:2e3},{icon:Va,title:"访问量",totalTitle:"总访问量",totalValue:5e5,value:2e4},{icon:$a,title:"下载量",totalTitle:"总下载量",totalValue:12e4,value:8e3},{icon:wa,title:"使用量",totalTitle:"总使用量",totalValue:5e4,value:5e3}],s=[{label:"流量趋势",value:"trends"},{label:"月访问量",value:"visits"}];return(a,i)=>(c(),d("div",Sa,[n(l(Ca),{items:e}),n(l(ka),{tabs:s,class:"mt-5"},{trends:r(()=>[n(Z)]),visits:r(()=>[n(sa)]),_:1}),L("div",Fa,[n(l($),{class:"mt-5 md:mr-4 md:mt-0 md:w-1/3",title:"访问数量"},{default:r(()=>[n(aa)]),_:1}),n(l($),{class:"mt-5 md:mr-4 md:mt-0 md:w-1/3",title:"访问来源"},{default:r(()=>[n(ta)]),_:1}),n(l($),{class:"mt-5 md:mt-0 md:w-1/3",title:"访问来源"},{default:r(()=>[n(ea)]),_:1})])]))}});export{Qa as default};
