var ae=Object.defineProperty,oe=Object.defineProperties;var ie=Object.getOwnPropertyDescriptors;var H=Object.getOwnPropertySymbols;var re=Object.prototype.hasOwnProperty,le=Object.prototype.propertyIsEnumerable;var Z=(i,o,s)=>o in i?ae(i,o,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[o]=s,U=(i,o)=>{for(var s in o||(o={}))re.call(o,s)&&Z(i,s,o[s]);if(H)for(var s of H(o))le.call(o,s)&&Z(i,s,o[s]);return i},z=(i,o)=>oe(i,ie(o));var ee=(i,o,s)=>new Promise((c,r)=>{var u=p=>{try{l(s.next(p))}catch(v){r(v)}},e=p=>{try{l(s.throw(p))}catch(v){r(v)}},l=p=>p.done?c(p.value):Promise.resolve(p.value).then(u,e);l((s=s.apply(i,o)).next())});import{b2 as ce,bl as ue,bm as fe,bk as de,aS as f,bn as X,aT as me,bo as pe,aU as K,bj as he,az as ge,bp as be}from"./bootstrap-CMNRQ0xm.js";import{a4 as B,J as W,av as _,ab as m,aw as F,a8 as T,by as V,P as Q,aV as j,x as h,a7 as t,ac as g,aa as A,ai as x,aj as w,a_ as te,a$ as ye,T as ne,Y as ve,ao as we,bJ as _e,b6 as se,aW as $e,aB as E,a9 as ke,aq as R,Q as Pe}from"../jse/index-index-DjeMElj0.js";import{T as Se}from"./auth-title-DGIEX_9J.js";import{M as Te,a as Me,b as Le,c as Re}from"./index-B2rlViSd.js";import{u as xe,s as O,b as Ee}from"./use-vben-form-Cz42CK4R.js";const Be=B({__name:"spine-text",props:{animationDuration:{default:2},animationIterationCount:{default:"infinite"}},setup(i){const o=W(()=>({animation:`shine ${i.animationDuration}s linear ${i.animationIterationCount}`}));return(s,c)=>(m(),_("div",{style:F(o.value),class:"vben-spine-text !bg-clip-text text-transparent"},[T(s.$slots,"default")],4))}}),Ce=B({__name:"slider-captcha-action",props:{actionStyle:{},isPassing:{type:Boolean},toLeft:{type:Boolean}},setup(i,{expose:o}){const s=i,c=V("actionRef"),r=Q("0"),u=W(()=>{const{actionStyle:l}=s;return z(U({},l),{left:r.value})}),e=W(()=>Number.parseInt(r.value)>10&&!s.isPassing);return o({getEl:()=>c.value,getStyle:()=>{var l;return(l=c==null?void 0:c.value)==null?void 0:l.style},setLeft:l=>{r.value=l}}),(l,p)=>(m(),_("div",{ref_key:"actionRef",ref:c,class:j([{"transition-width !left-0 duration-300":l.toLeft,"rounded-md":e.value},"bg-background dark:bg-accent absolute left-0 top-0 flex h-full cursor-move items-center justify-center px-3.5 shadow-md"]),style:F(u.value),name:"captcha-action"},[h(t(ce),{"is-passing":l.isPassing,class:"text-foreground/60 size-4"},{default:g(()=>[T(l.$slots,"icon",{},()=>[l.isPassing?(m(),A(t(fe),{key:1})):(m(),A(t(ue),{key:0}))])]),_:3},8,["is-passing"])],6))}}),De=B({__name:"slider-captcha-bar",props:{barStyle:{},toLeft:{type:Boolean}},setup(i,{expose:o}){const s=i,c=V("barRef"),r=Q("0"),u=W(()=>{const{barStyle:e}=s;return z(U({},e),{width:r.value})});return o({getEl:()=>c.value,setWidth:e=>{r.value=e}}),(e,l)=>(m(),_("div",{ref_key:"barRef",ref:c,class:j([e.toLeft&&"transition-width !w-0 duration-300","bg-success absolute h-full"]),style:F(u.value)},null,6))}}),Ve=B({__name:"slider-captcha-content",props:{contentStyle:{},isPassing:{type:Boolean},successText:{},text:{}},setup(i,{expose:o}){const s=i,c=V("contentRef"),r=W(()=>{const{contentStyle:u}=s;return U({},u)});return o({getEl:()=>c.value}),(u,e)=>(m(),_("div",{ref_key:"contentRef",ref:c,class:j([{[u.$style.success]:u.isPassing},"absolute top-0 flex size-full select-none items-center justify-center text-xs"]),style:F(r.value)},[T(u.$slots,"text",{},()=>[h(t(Be),{class:"flex h-full items-center"},{default:g(()=>[x(w(u.isPassing?u.successText:u.text),1)]),_:1})])],6))}}),Ae="_success_fwxn1_2",We={success:Ae},Ne={$style:We},Ie=de(Ve,[["__cssModules",Ne]]),Fe=B({__name:"index",props:te({class:{},actionStyle:{default:()=>({})},barStyle:{default:()=>({})},contentStyle:{default:()=>({})},wrapperStyle:{default:()=>({})},isSlot:{type:Boolean,default:!1},successText:{default:""},text:{default:""}},{modelValue:{type:Boolean,default:!1},modelModifiers:{}}),emits:te(["end","move","start","success"],["update:modelValue"]),setup(i,{expose:o,emit:s}){const c=i,r=s,u=ye(i,"modelValue"),e=ne({endTime:0,isMoving:!1,isPassing:!1,moveDistance:0,startTime:0,toLeft:!1});o({resume:q});const l=V("wrapperRef"),p=V("barRef"),v=V("contentRef"),$=V("actionRef");ve(()=>e.isPassing,n=>{if(n){const{endTime:b,startTime:y}=e,k=(b-y)/1e3;r("success",{isPassing:n,time:k.toFixed(1)}),u.value=n}}),we(()=>{e.isPassing=!!u.value});function N(n){return"pageX"in n?n.pageX:"touches"in n&&n.touches[0]?n.touches[0].pageX:0}function C(n){e.isPassing||$.value&&(r("start",n),e.moveDistance=N(n)-Number.parseInt($.value.getStyle().left.replace("px","")||"0",10),e.startTime=Date.now(),e.isMoving=!0)}function a(n){var P,S,D;const b=(S=(P=l.value)==null?void 0:P.offsetWidth)!=null?S:220,y=(D=n==null?void 0:n.offsetWidth)!=null?D:40,k=b-y-6;return{actionWidth:y,offset:k,wrapperWidth:b}}function d(n){const{isMoving:b,moveDistance:y}=e;if(b){const k=t($),P=t(p);if(!k||!P)return;const{actionWidth:S,offset:D,wrapperWidth:I}=a(k.getEl()),L=N(n)-y;r("move",{event:n,moveDistance:y,moveX:L}),L>0&&L<=D?(k.setLeft(`${L}px`),P.setWidth(`${L+S/2}px`)):L>D&&(k.setLeft(`${I-S}px`),P.setWidth(`${I-S/2}px`),c.isSlot||G())}}function M(n){const{isMoving:b,isPassing:y,moveDistance:k}=e;if(b&&!y){r("end",n);const P=$.value,S=t(p);if(!P||!S)return;const D=N(n)-k,{actionWidth:I,offset:L,wrapperWidth:J}=a(P.getEl());D<L?c.isSlot?setTimeout(()=>{if(u.value){const Y=t(v);Y&&(Y.getEl().style.width=`${Number.parseInt(S.getEl().style.width)}px`)}else q()},0):q():(P.setLeft(`${J-I}px`),S.setWidth(`${J-I/2}px`),G()),e.isMoving=!1}}function G(){if(c.isSlot){q();return}e.endTime=Date.now(),e.isPassing=!0,e.isMoving=!1}function q(){e.isMoving=!1,e.isPassing=!1,e.moveDistance=0,e.toLeft=!1,e.startTime=0,e.endTime=0;const n=t($),b=t(p),y=t(v);!n||!b||!y||(y.getEl().style.width="100%",e.toLeft=!0,_e(()=>{e.toLeft=!1,n.setLeft("0"),b.setWidth("0")},300))}return(n,b)=>(m(),_("div",{ref_key:"wrapperRef",ref:l,class:j(t($e)("border-border bg-background-deep relative flex h-10 w-full items-center overflow-hidden rounded-md border text-center",c.class)),style:F(n.wrapperStyle),onMouseleave:M,onMousemove:d,onMouseup:M,onTouchend:M,onTouchmove:d},[h(De,{ref_key:"barRef",ref:p,"bar-style":n.barStyle,"to-left":e.toLeft},null,8,["bar-style","to-left"]),h(Ie,{ref_key:"contentRef",ref:v,"content-style":n.contentStyle,"is-passing":e.isPassing,"success-text":n.successText||t(f)("ui.captcha.sliderSuccessText"),text:n.text||t(f)("ui.captcha.sliderDefaultText")},se({_:2},[n.$slots.text?{name:"text",fn:g(()=>[T(n.$slots,"text",{isPassing:e.isPassing})]),key:"0"}:void 0]),1032,["content-style","is-passing","success-text","text"]),h(Ce,{ref_key:"actionRef",ref:$,"action-style":n.actionStyle,"is-passing":e.isPassing,"to-left":e.toLeft,onMousedown:C,onTouchstart:C},se({_:2},[n.$slots.actionIcon?{name:"icon",fn:g(()=>[T(n.$slots,"actionIcon",{isPassing:e.isPassing})]),key:"0"}:void 0]),1032,["action-style","is-passing","to-left"])],38))}}),je={class:"w-full sm:mx-auto md:max-w-md"},qe={class:"mt-4 flex items-center justify-between"},Ue={class:"text-muted-foreground text-center text-xs uppercase"},Xe={class:"mt-4 flex flex-wrap justify-center"},ze=B({name:"ThirdPartyLogin",__name:"third-party-login",setup(i){return(o,s)=>(m(),_("div",je,[E("div",qe,[s[0]||(s[0]=E("span",{class:"border-input w-[35%] border-b dark:border-gray-600"},null,-1)),E("span",Ue,w(t(f)("authentication.thirdPartyLogin")),1),s[1]||(s[1]=E("span",{class:"border-input w-[35%] border-b dark:border-gray-600"},null,-1))]),E("div",Xe,[h(t(X),{class:"mb-3"},{default:g(()=>[h(t(Te))]),_:1}),h(t(X),{class:"mb-3"},{default:g(()=>[h(t(Me))]),_:1}),h(t(X),{class:"mb-3"},{default:g(()=>[h(t(Le))]),_:1}),h(t(X),{class:"mb-3"},{default:g(()=>[h(t(Re))]),_:1})])]))}}),Ke=["onKeydown"],Oe={class:"text-muted-foreground"},Qe={key:0,class:"mb-6 flex justify-between"},Ge={class:"flex-center"},Je={key:1,class:"mb-2 mt-4 flex items-center justify-between"},Ye={key:0,class:"mt-3 text-center text-sm"},He=B({name:"AuthenticationLogin",__name:"login",props:{formSchema:{default:()=>[]},codeLoginPath:{default:"/auth/code-login"},forgetPasswordPath:{default:"/auth/forget-password"},loading:{type:Boolean,default:!1},qrCodeLoginPath:{default:"/auth/qrcode-login"},registerPath:{default:"/auth/register"},showCodeLogin:{type:Boolean,default:!0},showForgetPassword:{type:Boolean,default:!0},showQrcodeLogin:{type:Boolean,default:!0},showRegister:{type:Boolean,default:!0},showRememberMe:{type:Boolean,default:!0},showThirdPartyLogin:{type:Boolean,default:!0},subTitle:{default:""},title:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(i,{expose:o,emit:s}){const c=i,r=s,[u,e]=xe(ne({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:W(()=>c.formSchema),showDefaultActions:!1})),l=me(),p=`REMEMBER_ME_USERNAME_${location.hostname}`,v=localStorage.getItem(p)||"",$=Q(!!v);function N(){return ee(this,null,function*(){const{valid:a}=yield e.validate(),d=yield e.getValues();a&&(localStorage.setItem(p,$.value?d==null?void 0:d.username:""),r("submit",d))})}function C(a){l.push(a)}return ke(()=>{v&&e.setFieldValue("username",v)}),o({getFormApi:()=>e}),(a,d)=>(m(),_("div",{onKeydown:he(ge(N,["prevent"]),["enter"])},[T(a.$slots,"title",{},()=>[h(Se,null,{desc:g(()=>[E("span",Oe,[T(a.$slots,"subTitle",{},()=>[x(w(a.subTitle||t(f)("authentication.loginSubtitle")),1)])])]),default:g(()=>[T(a.$slots,"title",{},()=>[x(w(a.title||`${t(f)("authentication.welcomeBack")} 👋🏻`),1)])]),_:3})]),h(t(u)),a.showRememberMe||a.showForgetPassword?(m(),_("div",Qe,[E("div",Ge,[a.showRememberMe?(m(),A(t(pe),{key:0,checked:$.value,"onUpdate:checked":d[0]||(d[0]=M=>$.value=M),name:"rememberMe"},{default:g(()=>[x(w(t(f)("authentication.rememberMe")),1)]),_:1},8,["checked"])):R("",!0)]),a.showForgetPassword?(m(),_("span",{key:0,class:"vben-link text-sm font-normal",onClick:d[1]||(d[1]=M=>C(a.forgetPasswordPath))},w(t(f)("authentication.forgetPassword")),1)):R("",!0)])):R("",!0),h(t(K),{class:j([{"cursor-wait":a.loading},"w-full"]),loading:a.loading,"aria-label":"login",onClick:N},{default:g(()=>[x(w(a.submitButtonText||t(f)("common.login")),1)]),_:1},8,["class","loading"]),a.showCodeLogin||a.showQrcodeLogin?(m(),_("div",Je,[a.showCodeLogin?(m(),A(t(K),{key:0,class:"w-1/2",variant:"outline",onClick:d[2]||(d[2]=M=>C(a.codeLoginPath))},{default:g(()=>[x(w(t(f)("authentication.mobileLogin")),1)]),_:1})):R("",!0),a.showQrcodeLogin?(m(),A(t(K),{key:1,class:"ml-4 w-1/2",variant:"outline",onClick:d[3]||(d[3]=M=>C(a.qrCodeLoginPath))},{default:g(()=>[x(w(t(f)("authentication.qrcodeLogin")),1)]),_:1})):R("",!0)])):R("",!0),T(a.$slots,"third-party-login",{},()=>[a.showThirdPartyLogin?(m(),A(ze,{key:0})):R("",!0)]),T(a.$slots,"to-register",{},()=>[a.showRegister?(m(),_("div",Ye,[x(w(t(f)("authentication.accountTip"))+" ",1),E("span",{class:"vben-link text-sm font-normal",onClick:d[4]||(d[4]=M=>C(a.registerPath))},w(t(f)("authentication.createAccount")),1)])):R("",!0)])],40,Ke))}}),ot=B({name:"Login",__name:"login",setup(i){const o=be(),s=[{label:"Super",value:"vben"},{label:"Admin",value:"admin"},{label:"User",value:"jack"}],c=W(()=>[{component:"VbenSelect",componentProps:{options:s,placeholder:f("authentication.selectAccount")},fieldName:"selectAccount",label:f("authentication.selectAccount"),rules:O().min(1,{message:f("authentication.selectAccount")}).optional().default("vben")},{component:"VbenInput",componentProps:{placeholder:f("authentication.usernameTip")},dependencies:{trigger(r,u){if(r.selectAccount){const e=s.find(l=>l.value===r.selectAccount);e&&u.setValues({password:"123456",username:e.value})}},triggerFields:["selectAccount"]},fieldName:"username",label:f("authentication.username"),rules:O().min(1,{message:f("authentication.usernameTip")})},{component:"VbenInputPassword",componentProps:{placeholder:f("authentication.password")},fieldName:"password",label:f("authentication.password"),rules:O().min(1,{message:f("authentication.passwordTip")})},{component:Pe(Fe),fieldName:"captcha",rules:Ee().refine(r=>r,{message:f("authentication.verifyRequiredTip")})}]);return(r,u)=>(m(),A(t(He),{"form-schema":c.value,loading:t(o).loginLoading,onSubmit:t(o).authLogin},null,8,["form-schema","loading","onSubmit"]))}});export{ot as _};
