import{b,d as B,e as T,f as w,c as A,_ as v,g as y,N as S,i as V,h as C,a as F}from"./layout.vue_vue_type_script_setup_true_lang-B3NNuOvu.js";import{A as N,_ as O,a as U}from"./authentication-D7VF4q3A.js";import{i as E,h as G}from"./theme-toggle.vue_vue_type_script_setup_true_lang-D_vzbc9t.js";import{bk as o}from"./bootstrap-CMNRQ0xm.js";import{P as a,av as t,ab as s}from"../jse/index-index-DjeMElj0.js";import"./avatar.vue_vue_type_script_setup_true_lang-B98nTz3p.js";import"./use-vben-form-Cz42CK4R.js";import"./render-content.vue_vue_type_script_lang-Ce0l9fmu.js";import"./TabsList.vue_vue_type_script_setup_true_lang-CWFpQvFK.js";import"./rotate-cw-Dgfir1M6.js";import"./index-B2rlViSd.js";import"./index-DbVlIpuM.js";const r=a(!1);function P(){function e(){r.value=!0}return{handleOpenPreference:e,openPreferences:r}}const n={};function c(e,i){return s(),t("div")}const k=o(n,[["render",c]]);export{N as AuthPageLayout,O as AuthenticationColorToggle,U as AuthenticationLayoutToggle,b as BasicLayout,B as Breadcrumb,T as CheckUpdates,w as GlobalSearch,A as IFrameRouterView,k as IFrameView,E as LanguageToggle,v as LockScreen,y as LockScreenModal,S as Notification,V as Preferences,C as PreferencesButton,G as ThemeToggle,F as UserDropdown,P as useOpenPreferences};
