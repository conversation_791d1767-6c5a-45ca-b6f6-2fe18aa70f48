import{a4 as i,aa as p,ab as l,ac as c,a8 as h,aV as n,a7 as s,aW as _,af as y,ag as b,ad as z,J as u,av as f,aw as m,x as d,aq as g,ai as x,aj as C}from"../jse/index-index-DjeMElj0.js";import{bA as k,bB as w,bC as B,bD as $}from"./bootstrap-CMNRQ0xm.js";const A=k("inline-flex items-center justify-center font-normal text-foreground select-none shrink-0 bg-secondary overflow-hidden",{variants:{shape:{circle:"rounded-full",square:"rounded-md"},size:{base:"h-16 w-16 text-2xl",lg:"h-32 w-32 text-5xl",sm:"h-10 w-10 text-xs"}}}),P=i({__name:"Avatar",props:{class:{},shape:{default:"circle"},size:{default:"sm"}},setup(t){const e=t;return(a,o)=>(l(),p(s(w),{class:n(s(_)(s(A)({size:a.size,shape:a.shape}),e.class))},{default:c(()=>[h(a.$slots,"default")]),_:3},8,["class"]))}}),S=i({__name:"AvatarFallback",props:{delayMs:{},asChild:{type:Boolean},as:{}},setup(t){const e=t;return(a,o)=>(l(),p(s(B),y(b(e)),{default:c(()=>[h(a.$slots,"default")]),_:3},16))}}),V=i({__name:"AvatarImage",props:{src:{},referrerPolicy:{},asChild:{type:Boolean},as:{}},setup(t){const e=t;return(a,o)=>(l(),p(s($),z(e,{class:"h-full w-full object-cover"}),null,16))}}),D=i({inheritAttrs:!1,__name:"avatar",props:{alt:{default:"avatar"},class:{},dot:{type:Boolean,default:!1},dotClass:{default:"bg-green-500"},fit:{default:"cover"},size:{},delayMs:{},asChild:{type:Boolean},as:{default:"button"},src:{},referrerPolicy:{}},setup(t){const e=t,a=u(()=>{const{fit:r}=e;return r?{objectFit:r}:{}}),o=u(()=>e.alt.slice(-2).toUpperCase()),v=u(()=>e.size!==void 0&&e.size>0?{height:`${e.size}px`,width:`${e.size}px`}:{});return(r,j)=>(l(),f("div",{class:n([e.class,"relative flex flex-shrink-0 items-center"]),style:m(v.value)},[d(s(P),{class:n([e.class,"size-full"])},{default:c(()=>[d(s(V),{alt:r.alt,src:r.src,style:m(a.value)},null,8,["alt","src","style"]),d(s(S),null,{default:c(()=>[x(C(o.value),1)]),_:1})]),_:1},8,["class"]),r.dot?(l(),f("span",{key:0,class:n([r.dotClass,"border-background absolute bottom-0 right-0 size-3 rounded-full border-2"])},null,2)):g("",!0)],6))}});export{D as _};
