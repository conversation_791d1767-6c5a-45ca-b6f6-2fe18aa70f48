var T=(m,u,n)=>new Promise((l,t)=>{var c=o=>{try{s(n.next(o))}catch(r){t(r)}},b=o=>{try{s(n.throw(o))}catch(r){t(r)}},s=o=>o.done?l(o.value):Promise.resolve(o.value).then(c,b);s((n=n.apply(m,u)).next())});import{aT as $,aS as e,aU as B}from"./bootstrap-CMNRQ0xm.js";import{T as w}from"./auth-title-DGIEX_9J.js";import{u as x,s as C}from"./use-vben-form-Cz42CK4R.js";import{a4 as S,T as L,J as k,av as N,ab as V,x as f,ac as p,a8 as _,ai as h,aj as g,a7 as i,aB as P,aV as y,P as A,aa as E}from"../jse/index-index-DjeMElj0.js";import"./render-content.vue_vue_type_script_lang-Ce0l9fmu.js";const D={class:"text-muted-foreground"},F=S({name:"AuthenticationCodeLogin",__name:"code-login",props:{formSchema:{},loading:{type:Boolean,default:!1},loginPath:{default:"/auth/login"},title:{default:""},subTitle:{default:""},submitButtonText:{default:""}},emits:["submit"],setup(m,{expose:u,emit:n}){const l=m,t=n,c=$(),[b,s]=x(L({commonConfig:{hideLabel:!0,hideRequiredMark:!0},schema:k(()=>l.formSchema),showDefaultActions:!1}));function o(){return T(this,null,function*(){const{valid:a}=yield s.validate(),d=yield s.getValues();a&&t("submit",d)})}function r(){c.push(l.loginPath)}return u({getFormApi:()=>s}),(a,d)=>(V(),N("div",null,[f(w,null,{desc:p(()=>[P("span",D,[_(a.$slots,"subTitle",{},()=>[h(g(a.subTitle||i(e)("authentication.codeSubtitle")),1)])])]),default:p(()=>[_(a.$slots,"title",{},()=>[h(g(a.title||i(e)("authentication.welcomeBack"))+" 📲 ",1)])]),_:3}),f(i(b)),f(i(B),{class:y([{"cursor-wait":a.loading},"w-full"]),loading:a.loading,onClick:o},{default:p(()=>[_(a.$slots,"submitButtonText",{},()=>[h(g(a.submitButtonText||i(e)("common.login")),1)])]),_:3},8,["class","loading"]),f(i(B),{class:"mt-4 w-full",variant:"outline",onClick:d[0]||(d[0]=I=>r())},{default:p(()=>[h(g(i(e)("common.back")),1)]),_:1})]))}}),v=6,J=S({name:"CodeLogin",__name:"code-login",setup(m){const u=A(!1),n=k(()=>[{component:"VbenInput",componentProps:{placeholder:e("authentication.mobile")},fieldName:"phoneNumber",label:e("authentication.mobile"),rules:C().min(1,{message:e("authentication.mobileTip")}).refine(t=>/^\d{11}$/.test(t),{message:e("authentication.mobileErrortip")})},{component:"VbenPinInput",componentProps:{codeLength:v,createText:t=>t>0?e("authentication.sendText",[t]):e("authentication.sendCode"),placeholder:e("authentication.code")},fieldName:"code",label:e("authentication.code"),rules:C().length(v,{message:e("authentication.codeTip",[v])})}]);function l(t){return T(this,null,function*(){console.log(t)})}return(t,c)=>(V(),E(i(F),{"form-schema":n.value,loading:u.value,onSubmit:l},null,8,["form-schema","loading"]))}});export{J as default};
