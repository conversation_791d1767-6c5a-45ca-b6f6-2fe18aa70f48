import{_ as f}from"./bootstrap-CMNRQ0xm.js";const l=["blue","purple","cyan","green","magenta","pink","red","orange","yellow","volcano","geekblue","lime","gold"],y=(t,o,n,s,$)=>{const e=t/2,r=0,c=e,g=n*1/Math.sqrt(2),i=e-n*(1-1/Math.sqrt(2)),p=e-o*(1/Math.sqrt(2)),h=n*(Math.sqrt(2)-1)+o*(1/Math.sqrt(2)),x=2*e-p,d=h,m=2*e-g,b=i,v=2*e-r,C=c,u=e*Math.sqrt(2)+n*(Math.sqrt(2)-2),a=n*(Math.sqrt(2)-1);return{pointerEvents:"none",width:t,height:t,overflow:"hidden","&::after":{content:'""',position:"absolute",width:u,height:u,bottom:0,insetInline:0,margin:"auto",borderRadius:{_skip_check_:!0,value:`0 0 ${o}px 0`},transform:"translateY(50%) rotate(-135deg)",boxShadow:$,zIndex:0,background:"transparent"},"&::before":{position:"absolute",bottom:0,insetInlineStart:0,width:t,height:t/2,background:s,clipPath:{_multi_value_:!0,value:[`polygon(${a}px 100%, 50% ${a}px, ${2*e-a}px 100%, ${a}px 100%)`,`path('M ${r} ${c} A ${n} ${n} 0 0 0 ${g} ${i} L ${p} ${h} A ${o} ${o} 0 0 1 ${x} ${d} L ${m} ${b} A ${n} ${n} 0 0 0 ${v} ${C} Z')`]},content:'""'}}};function P(t,o){return l.reduce((n,s)=>{const $=t[`${s}-1`],e=t[`${s}-3`],r=t[`${s}-6`],c=t[`${s}-7`];return f(f({},n),o(s,{lightColor:$,lightBorderColor:e,darkColor:r,textColor:c}))},{})}const M=l.map(t=>`${t}-inverse`),q=["success","processing","error","default","warning"];function I(t){return(arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0)?[...M,...l].includes(t):l.includes(t)}function k(t){return q.includes(t)}export{k as a,P as g,I as i,y as r};
