var Ns=Object.defineProperty,Vs=Object.defineProperties;var Fs=Object.getOwnPropertyDescriptors;var Ke=Object.getOwnPropertySymbols;var jt=Object.prototype.hasOwnProperty,Zt=Object.prototype.propertyIsEnumerable;var Mt=Math.pow,mt=(s,e,t)=>e in s?Ns(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t,l=(s,e)=>{for(var t in e||(e={}))jt.call(e,t)&&mt(s,t,e[t]);if(Ke)for(var t of Ke(e))Zt.call(e,t)&&mt(s,t,e[t]);return s},x=(s,e)=>Vs(s,Fs(e));var pt=(s,e)=>{var t={};for(var n in s)jt.call(s,n)&&e.indexOf(n)<0&&(t[n]=s[n]);if(s!=null&&Ke)for(var n of Ke(s))e.indexOf(n)<0&&Zt.call(s,n)&&(t[n]=s[n]);return t};var Y=(s,e,t)=>mt(s,typeof e!="symbol"?e+"":e,t);var R=(s,e,t)=>new Promise((n,r)=>{var a=c=>{try{o(t.next(c))}catch(u){r(u)}},i=c=>{try{o(t.throw(c))}catch(u){r(u)}},o=c=>c.done?n(c.value):Promise.resolve(c.value).then(a,i);o((t=t.apply(s,e)).next())});import{R as Pt,a4 as G,aa as P,ab as A,a7 as f,ac as F,a8 as Z,av as le,aV as Q,aW as q,an as Is,aF as Es,J as E,ad as re,aX as $s,x as ne,a_ as js,a$ as us,aB as Ge,ai as tt,aj as Le,P as ue,Y as be,am as Zs,r as Se,bz as Ms,bn as Ps,U as Dt,i as J,bA as We,bB as Ds,bC as Bt,o as Rt,bD as Bs,bE as Ls,as as zs,bF as _t,a as de,aq as B,F as bt,ah as He,aw as ds,bG as Us,bH as yt,by as fs,ax as qs,aA as Ws,b6 as hs,aC as At,af as ye,ag as ve,n as rt,bI as Hs,aP as Gs,aQ as Ys,a9 as ms,b3 as Lt,b5 as Js,a3 as Qs,aI as Xs,V as Ks,az as en,k as tn}from"../jse/index-index-DjeMElj0.js";import{aR as ps,aZ as sn,a_ as nn,a$ as rn,b0 as ys,b1 as an,b2 as on,b3 as ln,b4 as cn,b5 as vs,b6 as un,b7 as Nt,b8 as dn,b9 as fn,ba as st,bb as hn,bc as mn,bd as gs,be as pn,a8 as yn,a7 as vn,bf as gn,bg as _n,bh as bn,bi as kn,bj as xn}from"./bootstrap-CMNRQ0xm.js";import{_ as ze}from"./render-content.vue_vue_type_script_lang-Ce0l9fmu.js";const wn=ps("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);const Cn=ps("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),_s=Symbol();function ut(){const s=Pt(an),e=Pt(_s);if(!s)throw new Error("useFormField should be used within <FormField>");const{name:t}=s,n=e,r={error:ys(t),isDirty:rn(t),isTouched:nn(t),valid:sn(t)};return l({formDescriptionId:`${n}-form-item-description`,formItemId:`${n}-form-item`,formMessageId:`${n}-form-item-message`,id:n,name:t},r)}const Sn=G({__name:"FormControl",setup(s){const{error:e,formDescriptionId:t,formItemId:n,formMessageId:r}=ut();return(a,i)=>(A(),P(f(on),{id:f(n),"aria-describedby":f(e)?`${f(t)} ${f(r)}`:`${f(t)}`,"aria-invalid":!!f(e)},{default:F(()=>[Z(a.$slots,"default")]),_:3},8,["id","aria-describedby","aria-invalid"]))}}),On=["id"],Tn=G({__name:"FormDescription",props:{class:{}},setup(s){const e=s,{formDescriptionId:t}=ut();return(n,r)=>(A(),le("p",{id:f(t),class:Q(f(q)("text-muted-foreground text-sm",e.class))},[Z(n.$slots,"default")],10,On))}}),Rn=G({__name:"FormItem",props:{class:{}},setup(s){const e=s,t=Is();return Es(_s,t),(n,r)=>(A(),le("div",{class:Q(f(q)(e.class))},[Z(n.$slots,"default")],2))}}),An=G({__name:"Label",props:{for:{},asChild:{type:Boolean},as:{},class:{}},setup(s){const e=s,t=E(()=>{const a=e,{class:n}=a;return pt(a,["class"])});return(n,r)=>(A(),P(f(ln),re(t.value,{class:f(q)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",e.class)}),{default:F(()=>[Z(n.$slots,"default")]),_:3},16,["class"]))}}),Nn=G({__name:"FormLabel",props:{for:{},asChild:{type:Boolean},as:{},class:{}},setup(s){const e=s,{formItemId:t}=ut();return(n,r)=>(A(),P(f(An),{class:Q(f(q)(e.class)),for:f(t)},{default:F(()=>[Z(n.$slots,"default")]),_:3},8,["class","for"]))}}),zt=G({__name:"FormMessage",setup(s){const{formMessageId:e,name:t}=ut();return(n,r)=>(A(),P(f(cn),{id:f(e),name:$s(f(t)),as:"p",class:"text-destructive text-[0.8rem]"},null,8,["id","name"]))}}),Vn=G({inheritAttrs:!1,__name:"help-tooltip",props:{triggerClass:{}},setup(s){return(e,t)=>(A(),P(vs,{"delay-duration":300,side:"right"},{trigger:F(()=>[Z(e.$slots,"trigger",{},()=>[ne(f(Cn),{class:Q(f(q)("text-foreground/80 hover:text-foreground inline-flex size-5 cursor-pointer",e.triggerClass))},null,8,["class"])])]),default:F(()=>[Z(e.$slots,"default")]),_:3}))}}),Fn=G({__name:"expandable-arrow",props:js({class:{}},{modelValue:{default:!1},modelModifiers:{}}),emits:["update:modelValue"],setup(s){const e=s,t=us(s,"modelValue");return(n,r)=>(A(),le("div",{class:Q(f(q)("vben-link inline-flex items-center",e.class)),onClick:r[0]||(r[0]=a=>t.value=!t.value)},[Z(n.$slots,"default",{isExpanded:t.value},()=>[tt(Le(t.value),1)]),Ge("div",{class:Q([{"rotate-180":!t.value},"transition-transform duration-300"])},[Z(n.$slots,"icon",{},()=>[ne(f(un),{class:"size-4"})])],2)],2))}}),Ve=new WeakMap,nt=new WeakMap,at={current:[]};let vt=!1;const et=new Set,Ut=new Map;function bs(s){const e=Array.from(s).sort((t,n)=>t instanceof Fe&&t.options.deps.includes(n)?1:n instanceof Fe&&n.options.deps.includes(t)?-1:0);for(const t of e){if(at.current.includes(t))continue;at.current.push(t),t.recompute();const n=nt.get(t);if(n)for(const r of n){const a=Ve.get(r);a&&bs(a)}}}function In(s){s.listeners.forEach(e=>e({prevVal:s.prevState,currentVal:s.state}))}function En(s){s.listeners.forEach(e=>e({prevVal:s.prevState,currentVal:s.state}))}function $n(s){var e;if(et.add(s),!vt)try{for(vt=!0;et.size>0;){const t=Array.from(et);et.clear();for(const n of t){const r=(e=Ut.get(n))!=null?e:n.prevState;n.prevState=r,In(n)}for(const n of t){const r=Ve.get(n);r&&(at.current.push(n),bs(r))}for(const n of t){const r=Ve.get(n);if(r)for(const a of r)En(a)}}}finally{vt=!1,at.current=[],Ut.clear()}}function jn(s){return typeof s=="function"}class kt{constructor(e,t){this.listeners=new Set,this.subscribe=n=>{var r,a;this.listeners.add(n);const i=(a=(r=this.options)==null?void 0:r.onSubscribe)==null?void 0:a.call(r,n,this);return()=>{this.listeners.delete(n),i==null||i()}},this.prevState=e,this.state=e,this.options=t}setState(e){var t,n,r;this.prevState=this.state,(t=this.options)!=null&&t.updateFn?this.state=this.options.updateFn(this.prevState)(e):jn(e)?this.state=e(this.prevState):this.state=e,(r=(n=this.options)==null?void 0:n.onUpdate)==null||r.call(n),$n(this)}}class Fe{constructor(e){this.listeners=new Set,this._subscriptions=[],this.lastSeenDepValues=[],this.getDepVals=()=>{var r;const t=[],n=[];for(const a of this.options.deps)t.push(a.prevState),n.push(a.state);return this.lastSeenDepValues=n,{prevDepVals:t,currDepVals:n,prevVal:(r=this.prevState)!=null?r:void 0}},this.recompute=()=>{var t,n;this.prevState=this.state;const{prevDepVals:r,currDepVals:a,prevVal:i}=this.getDepVals();this.state=this.options.fn({prevDepVals:r,currDepVals:a,prevVal:i}),(n=(t=this.options).onUpdate)==null||n.call(t)},this.checkIfRecalculationNeededDeeply=()=>{for(const a of this.options.deps)a instanceof Fe&&a.checkIfRecalculationNeededDeeply();let t=!1;const n=this.lastSeenDepValues,{currDepVals:r}=this.getDepVals();for(let a=0;a<r.length;a++)if(r[a]!==n[a]){t=!0;break}t&&this.recompute()},this.mount=()=>(this.registerOnGraph(),this.checkIfRecalculationNeededDeeply(),()=>{this.unregisterFromGraph();for(const t of this._subscriptions)t()}),this.subscribe=t=>{var n,r;this.listeners.add(t);const a=(r=(n=this.options).onSubscribe)==null?void 0:r.call(n,t,this);return()=>{this.listeners.delete(t),a==null||a()}},this.options=e,this.state=e.fn({prevDepVals:void 0,prevVal:void 0,currDepVals:this.getDepVals().currDepVals})}registerOnGraph(e=this.options.deps){for(const t of e)if(t instanceof Fe)t.registerOnGraph(),this.registerOnGraph(t.options.deps);else if(t instanceof kt){let n=Ve.get(t);n||(n=new Set,Ve.set(t,n)),n.add(this);let r=nt.get(this);r||(r=new Set,nt.set(this,r)),r.add(t)}}unregisterFromGraph(e=this.options.deps){for(const t of e)if(t instanceof Fe)this.unregisterFromGraph(t.options.deps);else if(t instanceof kt){const n=Ve.get(t);n&&n.delete(this);const r=nt.get(this);r&&r.delete(t)}}}function Zn(s,e=t=>t){const t=ue(e(s.state));return be(()=>s,(n,r,a)=>{const i=n.subscribe(()=>{const o=e(n.state);Mn(Se(t.value),o)||(t.value=o)});a(()=>{i()})},{immediate:!0}),Zs(t)}function Mn(s,e){if(Object.is(s,e))return!0;if(typeof s!="object"||s===null||typeof e!="object"||e===null)return!1;if(s instanceof Map&&e instanceof Map){if(s.size!==e.size)return!1;for(const[n,r]of s)if(!e.has(n)||!Object.is(r,e.get(n)))return!1;return!0}if(s instanceof Set&&e instanceof Set){if(s.size!==e.size)return!1;for(const n of s)if(!e.has(n))return!1;return!0}const t=Object.keys(s);if(t.length!==Object.keys(e).length)return!1;for(let n=0;n<t.length;n++)if(!Object.prototype.hasOwnProperty.call(e,t[n])||!Object.is(s[t[n]],e[t[n]]))return!1;return!0}function Pn(){return{actionWrapperClass:"",collapsed:!1,collapsedRows:1,collapseTriggerResize:!1,commonConfig:{},handleReset:void 0,handleSubmit:void 0,handleValuesChange:void 0,layout:"horizontal",resetButtonOptions:{},schema:[],showCollapseButton:!1,showDefaultActions:!0,submitButtonOptions:{},submitOnChange:!1,submitOnEnter:!1,wrapperClass:"grid-cols-1"}}class Dn{constructor(e={}){Y(this,"form",{});Y(this,"isMounted",!1);Y(this,"state",null);Y(this,"stateHandler");Y(this,"store");Y(this,"componentRefMap",new Map);Y(this,"latestSubmissionValues",null);Y(this,"prevState",null);Y(this,"handleArrayToStringFields",e=>{var r;const t=(r=this.state)==null?void 0:r.arrayToStringFields;if(!t||!Array.isArray(t))return;const n=(a,i=",")=>{this.processFields(a,i,e,(o,c)=>Array.isArray(o)?o.join(c):o)};if(t.every(a=>typeof a=="string")){const a=t[t.length-1]||"",i=a.length===1?t.slice(0,-1):t,o=a.length===1?a:",";n(i,o);return}t.forEach(a=>{if(Array.isArray(a)){const[i,o=","]=a;if(!Array.isArray(i)){console.warn(`Invalid field configuration: fields should be an array of strings, got ${typeof i}`);return}n(i,o)}})});Y(this,"handleRangeTimeValue",e=>{var r;const t=l({},e),n=(r=this.state)==null?void 0:r.fieldMappingTime;return this.handleStringToArrayFields(t),!n||!Array.isArray(n)||n.forEach(([a,[i,o],c="YYYY-MM-DD"])=>{if(i&&o&&t[a]===null&&(Reflect.deleteProperty(t,i),Reflect.deleteProperty(t,o)),!t[a]){Reflect.deleteProperty(t,a);return}const[u,h]=t[a];if(c===null)t[i]=u,t[o]=h;else if(J(c))t[i]=c(u,i),t[o]=c(h,o);else{const[g,_]=Array.isArray(c)?c:[c,c];t[i]=u?Bt(u,g):void 0,t[o]=h?Bt(h,_):void 0}Reflect.deleteProperty(t,a)}),t});Y(this,"handleStringToArrayFields",e=>{var r;const t=(r=this.state)==null?void 0:r.arrayToStringFields;if(!t||!Array.isArray(t))return;const n=(a,i=",")=>{this.processFields(a,i,e,(o,c)=>{if(typeof o!="string")return o;if(o==="")return[];const u=c.replaceAll(/[.*+?^${}()|[\]\\]/g,String.raw`\$&`);return o.split(new RegExp(u))})};if(t.every(a=>typeof a=="string")){const a=t[t.length-1]||"",i=a.length===1?t.slice(0,-1):t,o=a.length===1?a:",";n(i,o);return}t.forEach(a=>{if(Array.isArray(a)){const[i,o=","]=a;if(Array.isArray(i))n(i,o);else if(typeof e[i]=="string"){const c=e[i];if(c==="")e[i]=[];else{const u=o.replaceAll(/[.*+?^${}()|[\]\\]/g,String.raw`\$&`);e[i]=c.split(new RegExp(u))}}}})});Y(this,"processFields",(e,t,n,r)=>{e.forEach(a=>{const i=n[a];i!=null&&(n[a]=r(i,t))})});const t=pt(e,[]),n=Pn();this.store=new kt(l(l({},n),t),{onUpdate:()=>{this.prevState=this.state,this.state=this.store.state,this.updateState()}}),this.state=this.store.state,this.stateHandler=new Ms,Ps(this)}getFieldComponentRef(e){var n,r;let t=this.componentRefMap.has(e)?this.componentRefMap.get(e):void 0;return t&&t.$.type.name==="AsyncComponentWrapper"&&t.$.subTree.ref&&(Array.isArray(t.$.subTree.ref)?t.$.subTree.ref.length>0&&Dt((n=t.$.subTree.ref[0])==null?void 0:n.r)&&(t=(r=t.$.subTree.ref[0])==null?void 0:r.r.value):Dt(t.$.subTree.ref.r)&&(t=t.$.subTree.ref.r.value)),t}getFocusedField(){for(const e of this.componentRefMap.keys()){const t=this.getFieldComponentRef(e);if(t){let n=null;if(t instanceof HTMLElement?n=t:t.$el instanceof HTMLElement&&(n=t.$el),!n)continue;if(n===document.activeElement||n.contains(document.activeElement))return e}}}getLatestSubmissionValues(){return this.latestSubmissionValues||{}}getState(){return this.state}getValues(){return R(this,null,function*(){const e=yield this.getForm();return e.values?this.handleRangeTimeValue(e.values):{}})}isFieldValid(e){return R(this,null,function*(){return(yield this.getForm()).isFieldValid(e)})}merge(e){const t=[this,e],n=new Proxy(e,{get(r,a){return a==="merge"?i=>(t.push(i),n):a==="submitAllForm"?(i=!0)=>R(null,null,function*(){try{const o=yield Promise.all(t.map(c=>R(null,null,function*(){return(yield c.validate()).valid?Se((yield c.getValues())||{}):void 0})));return i?Object.assign({},...o):o}catch(o){console.error("Validation error:",o)}}):r[a]}});return n}mount(e,t){this.isMounted||(Object.assign(this.form,e),this.stateHandler.setConditionTrue(),this.setLatestSubmissionValues(l({},Se(this.handleRangeTimeValue(this.form.values)))),this.componentRefMap=t,this.isMounted=!0)}removeSchemaByFields(e){return R(this,null,function*(){var a,i;const t=new Set(e),r=((i=(a=this.state)==null?void 0:a.schema)!=null?i:[]).filter(o=>!t.has(o.fieldName));this.setState({schema:r})})}resetForm(e,t){return R(this,null,function*(){return(yield this.getForm()).resetForm(e,t)})}resetValidate(){return R(this,null,function*(){const e=yield this.getForm();Object.keys(e.errors.value).forEach(n=>{e.setFieldError(n,void 0)})})}setFieldValue(e,t,n){return R(this,null,function*(){(yield this.getForm()).setFieldValue(e,t,n)})}setLatestSubmissionValues(e){this.latestSubmissionValues=l({},Se(e))}setState(e){J(e)?this.store.setState(t=>We(e(t),t)):this.store.setState(t=>We(e,t))}setValues(e,t=!0,n=!1){return R(this,null,function*(){const r=yield this.getForm();if(!t){r.setValues(e,n);return}const a=Ds((o,c,u)=>(c in o&&(o[c]=!Array.isArray(o[c])&&Rt(o[c])&&!Bs(o[c])&&!Ls(o[c])?a(o[c],u):u),!0)),i=a(e,r.values);this.handleStringToArrayFields(i),r.setValues(i,n)})}submitForm(e){return R(this,null,function*(){var r,a;e==null||e.preventDefault(),e==null||e.stopPropagation(),yield(yield this.getForm()).submitForm();const n=Se(yield this.getValues());return this.handleArrayToStringFields(n),yield(a=(r=this.state)==null?void 0:r.handleSubmit)==null?void 0:a.call(r,n),n})}unmount(){var e,t;(t=(e=this.form)==null?void 0:e.resetForm)==null||t.call(e),this.latestSubmissionValues=null,this.isMounted=!1,this.stateHandler.reset()}updateSchema(e){var i,o;const t=[...e];if(!t.every(c=>Reflect.has(c,"fieldName")&&c.fieldName)){console.error("All items in the schema array must have a valid `fieldName` property to be updated");return}const r=[...(o=(i=this.state)==null?void 0:i.schema)!=null?o:[]],a={};t.forEach(c=>{c.fieldName&&(a[c.fieldName]=c)}),r.forEach((c,u)=>{const h=a[c.fieldName];h&&(r[u]=We(h,c))}),this.setState({schema:r})}validate(e){return R(this,null,function*(){var r;const n=yield(yield this.getForm()).validate(e);return Object.keys((r=n==null?void 0:n.errors)!=null?r:{}).length>0&&console.error("validate error",n==null?void 0:n.errors),n})}validateAndSubmitForm(){return R(this,null,function*(){const e=yield this.getForm(),{valid:t}=yield e.validate();if(t)return yield this.submitForm()})}validateField(e,t){return R(this,null,function*(){var a;const r=yield(yield this.getForm()).validateField(e,t);return Object.keys((a=r==null?void 0:r.errors)!=null?a:{}).length>0&&console.error("validate error",r==null?void 0:r.errors),r})}getForm(){return R(this,null,function*(){var e;if(this.isMounted||(yield this.stateHandler.waitForCondition()),!((e=this.form)!=null&&e.meta))throw new Error("<VbenForm /> is not mounted");return this.form})}updateState(){var n,r,a,i,o,c;const e=(r=(n=this.state)==null?void 0:n.schema)!=null?r:[],t=(i=(a=this.prevState)==null?void 0:a.schema)!=null?i:[];if(e.length<t.length){const u=new Set(e.map(g=>g.fieldName)),h=t.filter(g=>!u.has(g.fieldName));for(const g of h)(c=(o=this.form)==null?void 0:o.setFieldValue)==null||c.call(o,g.fieldName,void 0)}}}var N;(function(s){s.assertEqual=r=>{};function e(r){}s.assertIs=e;function t(r){throw new Error}s.assertNever=t,s.arrayToEnum=r=>{const a={};for(const i of r)a[i]=i;return a},s.getValidEnumValues=r=>{const a=s.objectKeys(r).filter(o=>typeof r[r[o]]!="number"),i={};for(const o of a)i[o]=r[o];return s.objectValues(i)},s.objectValues=r=>s.objectKeys(r).map(function(a){return r[a]}),s.objectKeys=typeof Object.keys=="function"?r=>Object.keys(r):r=>{const a=[];for(const i in r)Object.prototype.hasOwnProperty.call(r,i)&&a.push(i);return a},s.find=(r,a)=>{for(const i of r)if(a(i))return i},s.isInteger=typeof Number.isInteger=="function"?r=>Number.isInteger(r):r=>typeof r=="number"&&Number.isFinite(r)&&Math.floor(r)===r;function n(r,a=" | "){return r.map(i=>typeof i=="string"?`'${i}'`:i).join(a)}s.joinValues=n,s.jsonStringifyReplacer=(r,a)=>typeof a=="bigint"?a.toString():a})(N||(N={}));var qt;(function(s){s.mergeShapes=(e,t)=>l(l({},e),t)})(qt||(qt={}));const y=N.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),ge=s=>{switch(typeof s){case"undefined":return y.undefined;case"string":return y.string;case"number":return Number.isNaN(s)?y.nan:y.number;case"boolean":return y.boolean;case"function":return y.function;case"bigint":return y.bigint;case"symbol":return y.symbol;case"object":return Array.isArray(s)?y.array:s===null?y.null:s.then&&typeof s.then=="function"&&s.catch&&typeof s.catch=="function"?y.promise:typeof Map!="undefined"&&s instanceof Map?y.map:typeof Set!="undefined"&&s instanceof Set?y.set:typeof Date!="undefined"&&s instanceof Date?y.date:y.object;default:return y.unknown}},d=N.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class he extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(a){return a.message},n={_errors:[]},r=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(r);else if(i.code==="invalid_return_type")r(i.returnTypeError);else if(i.code==="invalid_arguments")r(i.argumentsError);else if(i.path.length===0)n._errors.push(t(i));else{let o=n,c=0;for(;c<i.path.length;){const u=i.path[c];c===i.path.length-1?(o[u]=o[u]||{_errors:[]},o[u]._errors.push(t(i))):o[u]=o[u]||{_errors:[]},o=o[u],c++}}};return r(this),n}static assert(e){if(!(e instanceof he))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,N.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},n=[];for(const r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}he.create=s=>new he(s);const xt=(s,e)=>{let t;switch(s.code){case d.invalid_type:s.received===y.undefined?t="Required":t=`Expected ${s.expected}, received ${s.received}`;break;case d.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(s.expected,N.jsonStringifyReplacer)}`;break;case d.unrecognized_keys:t=`Unrecognized key(s) in object: ${N.joinValues(s.keys,", ")}`;break;case d.invalid_union:t="Invalid input";break;case d.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${N.joinValues(s.options)}`;break;case d.invalid_enum_value:t=`Invalid enum value. Expected ${N.joinValues(s.options)}, received '${s.received}'`;break;case d.invalid_arguments:t="Invalid function arguments";break;case d.invalid_return_type:t="Invalid function return type";break;case d.invalid_date:t="Invalid date";break;case d.invalid_string:typeof s.validation=="object"?"includes"in s.validation?(t=`Invalid input: must include "${s.validation.includes}"`,typeof s.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${s.validation.position}`)):"startsWith"in s.validation?t=`Invalid input: must start with "${s.validation.startsWith}"`:"endsWith"in s.validation?t=`Invalid input: must end with "${s.validation.endsWith}"`:N.assertNever(s.validation):s.validation!=="regex"?t=`Invalid ${s.validation}`:t="Invalid";break;case d.too_small:s.type==="array"?t=`Array must contain ${s.exact?"exactly":s.inclusive?"at least":"more than"} ${s.minimum} element(s)`:s.type==="string"?t=`String must contain ${s.exact?"exactly":s.inclusive?"at least":"over"} ${s.minimum} character(s)`:s.type==="number"?t=`Number must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${s.minimum}`:s.type==="date"?t=`Date must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(s.minimum))}`:t="Invalid input";break;case d.too_big:s.type==="array"?t=`Array must contain ${s.exact?"exactly":s.inclusive?"at most":"less than"} ${s.maximum} element(s)`:s.type==="string"?t=`String must contain ${s.exact?"exactly":s.inclusive?"at most":"under"} ${s.maximum} character(s)`:s.type==="number"?t=`Number must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="bigint"?t=`BigInt must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="date"?t=`Date must be ${s.exact?"exactly":s.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(s.maximum))}`:t="Invalid input";break;case d.custom:t="Invalid input";break;case d.invalid_intersection_types:t="Intersection results could not be merged";break;case d.not_multiple_of:t=`Number must be a multiple of ${s.multipleOf}`;break;case d.not_finite:t="Number must be finite";break;default:t=e.defaultError,N.assertNever(s)}return{message:t}};let Bn=xt;function Ln(){return Bn}const zn=s=>{const{data:e,path:t,errorMaps:n,issueData:r}=s,a=[...t,...r.path||[]],i=x(l({},r),{path:a});if(r.message!==void 0)return x(l({},r),{path:a,message:r.message});let o="";const c=n.filter(u=>!!u).slice().reverse();for(const u of c)o=u(i,{data:e,defaultError:o}).message;return x(l({},r),{path:a,message:o})};function m(s,e){const t=Ln(),n=zn({issueData:e,data:s.data,path:s.path,errorMaps:[s.common.contextualErrorMap,s.schemaErrorMap,t,t===xt?void 0:xt].filter(r=>!!r)});s.common.issues.push(n)}class W{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const r of t){if(r.status==="aborted")return k;r.status==="dirty"&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static mergeObjectAsync(e,t){return R(this,null,function*(){const n=[];for(const r of t){const a=yield r.key,i=yield r.value;n.push({key:a,value:i})}return W.mergeObjectSync(e,n)})}static mergeObjectSync(e,t){const n={};for(const r of t){const{key:a,value:i}=r;if(a.status==="aborted"||i.status==="aborted")return k;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value!=="__proto__"&&(typeof i.value!="undefined"||r.alwaysSet)&&(n[a.value]=i.value)}return{status:e.value,value:n}}}const k=Object.freeze({status:"aborted"}),Ue=s=>({status:"dirty",value:s}),ee=s=>({status:"valid",value:s}),Wt=s=>s.status==="aborted",Ht=s=>s.status==="dirty",Ie=s=>s.status==="valid",it=s=>typeof Promise!="undefined"&&s instanceof Promise;var v;(function(s){s.errToObj=e=>typeof e=="string"?{message:e}:e||{},s.toString=e=>typeof e=="string"?e:e==null?void 0:e.message})(v||(v={}));class ce{constructor(e,t,n,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=r}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Gt=(s,e)=>{if(Ie(e))return{success:!0,data:e.value};if(!s.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new he(s.common.issues);return this._error=t,this._error}}};function w(s){if(!s)return{};const{errorMap:e,invalid_type_error:t,required_error:n,description:r}=s;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(i,o)=>{var u,h;const{message:c}=s;return i.code==="invalid_enum_value"?{message:c!=null?c:o.defaultError}:typeof o.data=="undefined"?{message:(u=c!=null?c:n)!=null?u:o.defaultError}:i.code!=="invalid_type"?{message:o.defaultError}:{message:(h=c!=null?c:t)!=null?h:o.defaultError}},description:r}}class O{get description(){return this._def.description}_getType(e){return ge(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:ge(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new W,ctx:{common:e.parent.common,data:e.data,parsedType:ge(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(it(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){var a;const n={common:{issues:[],async:(a=t==null?void 0:t.async)!=null?a:!1,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ge(e)},r=this._parseSync({data:e,path:n.path,parent:n});return Gt(n,r)}"~validate"(e){var n,r;const t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ge(e)};if(!this["~standard"].async)try{const a=this._parseSync({data:e,path:[],parent:t});return Ie(a)?{value:a.value}:{issues:t.common.issues}}catch(a){(r=(n=a==null?void 0:a.message)==null?void 0:n.toLowerCase())!=null&&r.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(a=>Ie(a)?{value:a.value}:{issues:t.common.issues})}parseAsync(e,t){return R(this,null,function*(){const n=yield this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error})}safeParseAsync(e,t){return R(this,null,function*(){const n={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ge(e)},r=this._parse({data:e,path:n.path,parent:n}),a=yield it(r)?r:Promise.resolve(r);return Gt(n,a)})}refine(e,t){const n=r=>typeof t=="string"||typeof t=="undefined"?{message:t}:typeof t=="function"?t(r):t;return this._refinement((r,a)=>{const i=e(r),o=()=>a.addIssue(l({code:d.custom},n(r)));return typeof Promise!="undefined"&&i instanceof Promise?i.then(c=>c?!0:(o(),!1)):i?!0:(o(),!1)})}refinement(e,t){return this._refinement((n,r)=>e(n)?!0:(r.addIssue(typeof t=="function"?t(n,r):t),!1))}_refinement(e){return new oe({schema:this,typeName:b.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return fe.create(this,this._def)}nullable(){return je.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ie.create(this)}promise(){return ct.create(this,this._def)}or(e){return Ee.create([this,e],this._def)}and(e){return Te.create(this,e,this._def)}transform(e){return new oe(x(l({},w(this._def)),{schema:this,typeName:b.ZodEffects,effect:{type:"transform",transform:e}}))}default(e){const t=typeof e=="function"?e:()=>e;return new Ze(x(l({},w(this._def)),{innerType:this,defaultValue:t,typeName:b.ZodDefault}))}brand(){return new fr(l({typeName:b.ZodBranded,type:this},w(this._def)))}catch(e){const t=typeof e=="function"?e:()=>e;return new Ct(x(l({},w(this._def)),{innerType:this,catchValue:t,typeName:b.ZodCatch}))}describe(e){const t=this.constructor;return new t(x(l({},this._def),{description:e}))}pipe(e){return Vt.create(this,e)}readonly(){return St.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Un=/^c[^\s-]{8,}$/i,qn=/^[0-9a-z]+$/,Wn=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Hn=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Gn=/^[a-z0-9_-]{21}$/i,Yn=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Jn=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Qn=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,Xn="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let gt;const Kn=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,er=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,tr=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,sr=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,nr=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,rr=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,ks="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",ar=new RegExp(`^${ks}$`);function xs(s){let e="[0-5]\\d";s.precision?e=`${e}\\.\\d{${s.precision}}`:s.precision==null&&(e=`${e}(\\.\\d+)?`);const t=s.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${e})${t}`}function ir(s){return new RegExp(`^${xs(s)}$`)}function or(s){let e=`${ks}T${xs(s)}`;const t=[];return t.push(s.local?"Z?":"Z"),s.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function lr(s,e){return!!((e==="v4"||!e)&&Kn.test(s)||(e==="v6"||!e)&&tr.test(s))}function cr(s,e){if(!Yn.test(s))return!1;try{const[t]=s.split("."),n=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),r=JSON.parse(atob(n));return!(typeof r!="object"||r===null||"typ"in r&&(r==null?void 0:r.typ)!=="JWT"||!r.alg||e&&r.alg!==e)}catch(t){return!1}}function ur(s,e){return!!((e==="v4"||!e)&&er.test(s)||(e==="v6"||!e)&&sr.test(s))}class ae extends O{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==y.string){const a=this._getOrReturnCtx(e);return m(a,{code:d.invalid_type,expected:y.string,received:a.parsedType}),k}const n=new W;let r;for(const a of this._def.checks)if(a.kind==="min")e.data.length<a.value&&(r=this._getOrReturnCtx(e,r),m(r,{code:d.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),n.dirty());else if(a.kind==="max")e.data.length>a.value&&(r=this._getOrReturnCtx(e,r),m(r,{code:d.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),n.dirty());else if(a.kind==="length"){const i=e.data.length>a.value,o=e.data.length<a.value;(i||o)&&(r=this._getOrReturnCtx(e,r),i?m(r,{code:d.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}):o&&m(r,{code:d.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}),n.dirty())}else if(a.kind==="email")Qn.test(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{validation:"email",code:d.invalid_string,message:a.message}),n.dirty());else if(a.kind==="emoji")gt||(gt=new RegExp(Xn,"u")),gt.test(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{validation:"emoji",code:d.invalid_string,message:a.message}),n.dirty());else if(a.kind==="uuid")Hn.test(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{validation:"uuid",code:d.invalid_string,message:a.message}),n.dirty());else if(a.kind==="nanoid")Gn.test(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{validation:"nanoid",code:d.invalid_string,message:a.message}),n.dirty());else if(a.kind==="cuid")Un.test(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{validation:"cuid",code:d.invalid_string,message:a.message}),n.dirty());else if(a.kind==="cuid2")qn.test(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{validation:"cuid2",code:d.invalid_string,message:a.message}),n.dirty());else if(a.kind==="ulid")Wn.test(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{validation:"ulid",code:d.invalid_string,message:a.message}),n.dirty());else if(a.kind==="url")try{new URL(e.data)}catch(i){r=this._getOrReturnCtx(e,r),m(r,{validation:"url",code:d.invalid_string,message:a.message}),n.dirty()}else a.kind==="regex"?(a.regex.lastIndex=0,a.regex.test(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{validation:"regex",code:d.invalid_string,message:a.message}),n.dirty())):a.kind==="trim"?e.data=e.data.trim():a.kind==="includes"?e.data.includes(a.value,a.position)||(r=this._getOrReturnCtx(e,r),m(r,{code:d.invalid_string,validation:{includes:a.value,position:a.position},message:a.message}),n.dirty()):a.kind==="toLowerCase"?e.data=e.data.toLowerCase():a.kind==="toUpperCase"?e.data=e.data.toUpperCase():a.kind==="startsWith"?e.data.startsWith(a.value)||(r=this._getOrReturnCtx(e,r),m(r,{code:d.invalid_string,validation:{startsWith:a.value},message:a.message}),n.dirty()):a.kind==="endsWith"?e.data.endsWith(a.value)||(r=this._getOrReturnCtx(e,r),m(r,{code:d.invalid_string,validation:{endsWith:a.value},message:a.message}),n.dirty()):a.kind==="datetime"?or(a).test(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{code:d.invalid_string,validation:"datetime",message:a.message}),n.dirty()):a.kind==="date"?ar.test(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{code:d.invalid_string,validation:"date",message:a.message}),n.dirty()):a.kind==="time"?ir(a).test(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{code:d.invalid_string,validation:"time",message:a.message}),n.dirty()):a.kind==="duration"?Jn.test(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{validation:"duration",code:d.invalid_string,message:a.message}),n.dirty()):a.kind==="ip"?lr(e.data,a.version)||(r=this._getOrReturnCtx(e,r),m(r,{validation:"ip",code:d.invalid_string,message:a.message}),n.dirty()):a.kind==="jwt"?cr(e.data,a.alg)||(r=this._getOrReturnCtx(e,r),m(r,{validation:"jwt",code:d.invalid_string,message:a.message}),n.dirty()):a.kind==="cidr"?ur(e.data,a.version)||(r=this._getOrReturnCtx(e,r),m(r,{validation:"cidr",code:d.invalid_string,message:a.message}),n.dirty()):a.kind==="base64"?nr.test(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{validation:"base64",code:d.invalid_string,message:a.message}),n.dirty()):a.kind==="base64url"?rr.test(e.data)||(r=this._getOrReturnCtx(e,r),m(r,{validation:"base64url",code:d.invalid_string,message:a.message}),n.dirty()):N.assertNever(a);return{status:n.value,value:e.data}}_regex(e,t,n){return this.refinement(r=>e.test(r),l({validation:t,code:d.invalid_string},v.errToObj(n)))}_addCheck(e){return new ae(x(l({},this._def),{checks:[...this._def.checks,e]}))}email(e){return this._addCheck(l({kind:"email"},v.errToObj(e)))}url(e){return this._addCheck(l({kind:"url"},v.errToObj(e)))}emoji(e){return this._addCheck(l({kind:"emoji"},v.errToObj(e)))}uuid(e){return this._addCheck(l({kind:"uuid"},v.errToObj(e)))}nanoid(e){return this._addCheck(l({kind:"nanoid"},v.errToObj(e)))}cuid(e){return this._addCheck(l({kind:"cuid"},v.errToObj(e)))}cuid2(e){return this._addCheck(l({kind:"cuid2"},v.errToObj(e)))}ulid(e){return this._addCheck(l({kind:"ulid"},v.errToObj(e)))}base64(e){return this._addCheck(l({kind:"base64"},v.errToObj(e)))}base64url(e){return this._addCheck(l({kind:"base64url"},v.errToObj(e)))}jwt(e){return this._addCheck(l({kind:"jwt"},v.errToObj(e)))}ip(e){return this._addCheck(l({kind:"ip"},v.errToObj(e)))}cidr(e){return this._addCheck(l({kind:"cidr"},v.errToObj(e)))}datetime(e){var t,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck(l({kind:"datetime",precision:typeof(e==null?void 0:e.precision)=="undefined"?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!=null?t:!1,local:(n=e==null?void 0:e.local)!=null?n:!1},v.errToObj(e==null?void 0:e.message)))}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck(l({kind:"time",precision:typeof(e==null?void 0:e.precision)=="undefined"?null:e==null?void 0:e.precision},v.errToObj(e==null?void 0:e.message)))}duration(e){return this._addCheck(l({kind:"duration"},v.errToObj(e)))}regex(e,t){return this._addCheck(l({kind:"regex",regex:e},v.errToObj(t)))}includes(e,t){return this._addCheck(l({kind:"includes",value:e,position:t==null?void 0:t.position},v.errToObj(t==null?void 0:t.message)))}startsWith(e,t){return this._addCheck(l({kind:"startsWith",value:e},v.errToObj(t)))}endsWith(e,t){return this._addCheck(l({kind:"endsWith",value:e},v.errToObj(t)))}min(e,t){return this._addCheck(l({kind:"min",value:e},v.errToObj(t)))}max(e,t){return this._addCheck(l({kind:"max",value:e},v.errToObj(t)))}length(e,t){return this._addCheck(l({kind:"length",value:e},v.errToObj(t)))}nonempty(e){return this.min(1,v.errToObj(e))}trim(){return new ae(x(l({},this._def),{checks:[...this._def.checks,{kind:"trim"}]}))}toLowerCase(){return new ae(x(l({},this._def),{checks:[...this._def.checks,{kind:"toLowerCase"}]}))}toUpperCase(){return new ae(x(l({},this._def),{checks:[...this._def.checks,{kind:"toUpperCase"}]}))}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}ae.create=s=>{var e;return new ae(l({checks:[],typeName:b.ZodString,coerce:(e=s==null?void 0:s.coerce)!=null?e:!1},w(s)))};function dr(s,e){const t=(s.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,r=t>n?t:n,a=Number.parseInt(s.toFixed(r).replace(".","")),i=Number.parseInt(e.toFixed(r).replace(".",""));return a%i/Mt(10,r)}class Oe extends O{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==y.number){const a=this._getOrReturnCtx(e);return m(a,{code:d.invalid_type,expected:y.number,received:a.parsedType}),k}let n;const r=new W;for(const a of this._def.checks)a.kind==="int"?N.isInteger(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{code:d.invalid_type,expected:"integer",received:"float",message:a.message}),r.dirty()):a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(n=this._getOrReturnCtx(e,n),m(n,{code:d.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(n=this._getOrReturnCtx(e,n),m(n,{code:d.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),r.dirty()):a.kind==="multipleOf"?dr(e.data,a.value)!==0&&(n=this._getOrReturnCtx(e,n),m(n,{code:d.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):a.kind==="finite"?Number.isFinite(e.data)||(n=this._getOrReturnCtx(e,n),m(n,{code:d.not_finite,message:a.message}),r.dirty()):N.assertNever(a);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,v.toString(t))}gt(e,t){return this.setLimit("min",e,!1,v.toString(t))}lte(e,t){return this.setLimit("max",e,!0,v.toString(t))}lt(e,t){return this.setLimit("max",e,!1,v.toString(t))}setLimit(e,t,n,r){return new Oe(x(l({},this._def),{checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:v.toString(r)}]}))}_addCheck(e){return new Oe(x(l({},this._def),{checks:[...this._def.checks,e]}))}int(e){return this._addCheck({kind:"int",message:v.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:v.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:v.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:v.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:v.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:v.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:v.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:v.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:v.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&N.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(t===null||n.value>t)&&(t=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}}Oe.create=s=>new Oe(l({checks:[],typeName:b.ZodNumber,coerce:(s==null?void 0:s.coerce)||!1},w(s)));class Ye extends O{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch(a){return this._getInvalidInput(e)}if(this._getType(e)!==y.bigint)return this._getInvalidInput(e);let n;const r=new W;for(const a of this._def.checks)a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(n=this._getOrReturnCtx(e,n),m(n,{code:d.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(n=this._getOrReturnCtx(e,n),m(n,{code:d.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),r.dirty()):a.kind==="multipleOf"?e.data%a.value!==BigInt(0)&&(n=this._getOrReturnCtx(e,n),m(n,{code:d.not_multiple_of,multipleOf:a.value,message:a.message}),r.dirty()):N.assertNever(a);return{status:r.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:y.bigint,received:t.parsedType}),k}gte(e,t){return this.setLimit("min",e,!0,v.toString(t))}gt(e,t){return this.setLimit("min",e,!1,v.toString(t))}lte(e,t){return this.setLimit("max",e,!0,v.toString(t))}lt(e,t){return this.setLimit("max",e,!1,v.toString(t))}setLimit(e,t,n,r){return new Ye(x(l({},this._def),{checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:v.toString(r)}]}))}_addCheck(e){return new Ye(x(l({},this._def),{checks:[...this._def.checks,e]}))}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:v.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:v.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:v.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:v.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:v.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}Ye.create=s=>{var e;return new Ye(l({checks:[],typeName:b.ZodBigInt,coerce:(e=s==null?void 0:s.coerce)!=null?e:!1},w(s)))};class ot extends O{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==y.boolean){const n=this._getOrReturnCtx(e);return m(n,{code:d.invalid_type,expected:y.boolean,received:n.parsedType}),k}return ee(e.data)}}ot.create=s=>new ot(l({typeName:b.ZodBoolean,coerce:(s==null?void 0:s.coerce)||!1},w(s)));class lt extends O{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==y.date){const a=this._getOrReturnCtx(e);return m(a,{code:d.invalid_type,expected:y.date,received:a.parsedType}),k}if(Number.isNaN(e.data.getTime())){const a=this._getOrReturnCtx(e);return m(a,{code:d.invalid_date}),k}const n=new W;let r;for(const a of this._def.checks)a.kind==="min"?e.data.getTime()<a.value&&(r=this._getOrReturnCtx(e,r),m(r,{code:d.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),n.dirty()):a.kind==="max"?e.data.getTime()>a.value&&(r=this._getOrReturnCtx(e,r),m(r,{code:d.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),n.dirty()):N.assertNever(a);return{status:n.value,value:new Date(e.data.getTime())}}_addCheck(e){return new lt(x(l({},this._def),{checks:[...this._def.checks,e]}))}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:v.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:v.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}lt.create=s=>new lt(l({checks:[],coerce:(s==null?void 0:s.coerce)||!1,typeName:b.ZodDate},w(s)));class Yt extends O{_parse(e){if(this._getType(e)!==y.symbol){const n=this._getOrReturnCtx(e);return m(n,{code:d.invalid_type,expected:y.symbol,received:n.parsedType}),k}return ee(e.data)}}Yt.create=s=>new Yt(l({typeName:b.ZodSymbol},w(s)));class Jt extends O{_parse(e){if(this._getType(e)!==y.undefined){const n=this._getOrReturnCtx(e);return m(n,{code:d.invalid_type,expected:y.undefined,received:n.parsedType}),k}return ee(e.data)}}Jt.create=s=>new Jt(l({typeName:b.ZodUndefined},w(s)));class Qt extends O{_parse(e){if(this._getType(e)!==y.null){const n=this._getOrReturnCtx(e);return m(n,{code:d.invalid_type,expected:y.null,received:n.parsedType}),k}return ee(e.data)}}Qt.create=s=>new Qt(l({typeName:b.ZodNull},w(s)));class Xt extends O{constructor(){super(...arguments),this._any=!0}_parse(e){return ee(e.data)}}Xt.create=s=>new Xt(l({typeName:b.ZodAny},w(s)));class Kt extends O{constructor(){super(...arguments),this._unknown=!0}_parse(e){return ee(e.data)}}Kt.create=s=>new Kt(l({typeName:b.ZodUnknown},w(s)));class ke extends O{_parse(e){const t=this._getOrReturnCtx(e);return m(t,{code:d.invalid_type,expected:y.never,received:t.parsedType}),k}}ke.create=s=>new ke(l({typeName:b.ZodNever},w(s)));class es extends O{_parse(e){if(this._getType(e)!==y.undefined){const n=this._getOrReturnCtx(e);return m(n,{code:d.invalid_type,expected:y.void,received:n.parsedType}),k}return ee(e.data)}}es.create=s=>new es(l({typeName:b.ZodVoid},w(s)));class ie extends O{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==y.array)return m(t,{code:d.invalid_type,expected:y.array,received:t.parsedType}),k;if(r.exactLength!==null){const i=t.data.length>r.exactLength.value,o=t.data.length<r.exactLength.value;(i||o)&&(m(t,{code:i?d.too_big:d.too_small,minimum:o?r.exactLength.value:void 0,maximum:i?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(r.minLength!==null&&t.data.length<r.minLength.value&&(m(t,{code:d.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),r.maxLength!==null&&t.data.length>r.maxLength.value&&(m(t,{code:d.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>r.type._parseAsync(new ce(t,i,t.path,o)))).then(i=>W.mergeArray(n,i));const a=[...t.data].map((i,o)=>r.type._parseSync(new ce(t,i,t.path,o)));return W.mergeArray(n,a)}get element(){return this._def.type}min(e,t){return new ie(x(l({},this._def),{minLength:{value:e,message:v.toString(t)}}))}max(e,t){return new ie(x(l({},this._def),{maxLength:{value:e,message:v.toString(t)}}))}length(e,t){return new ie(x(l({},this._def),{exactLength:{value:e,message:v.toString(t)}}))}nonempty(e){return this.min(1,e)}}ie.create=(s,e)=>new ie(l({type:s,minLength:null,maxLength:null,exactLength:null,typeName:b.ZodArray},w(e)));function Ne(s){if(s instanceof V){const e={};for(const t in s.shape){const n=s.shape[t];e[t]=fe.create(Ne(n))}return new V(x(l({},s._def),{shape:()=>e}))}else return s instanceof ie?new ie(x(l({},s._def),{type:Ne(s.element)})):s instanceof fe?fe.create(Ne(s.unwrap())):s instanceof je?je.create(Ne(s.unwrap())):s instanceof xe?xe.create(s.items.map(e=>Ne(e))):s}class V extends O{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=N.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==y.object){const u=this._getOrReturnCtx(e);return m(u,{code:d.invalid_type,expected:y.object,received:u.parsedType}),k}const{status:n,ctx:r}=this._processInputParams(e),{shape:a,keys:i}=this._getCached(),o=[];if(!(this._def.catchall instanceof ke&&this._def.unknownKeys==="strip"))for(const u in r.data)i.includes(u)||o.push(u);const c=[];for(const u of i){const h=a[u],g=r.data[u];c.push({key:{status:"valid",value:u},value:h._parse(new ce(r,g,r.path,u)),alwaysSet:u in r.data})}if(this._def.catchall instanceof ke){const u=this._def.unknownKeys;if(u==="passthrough")for(const h of o)c.push({key:{status:"valid",value:h},value:{status:"valid",value:r.data[h]}});else if(u==="strict")o.length>0&&(m(r,{code:d.unrecognized_keys,keys:o}),n.dirty());else if(u!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const u=this._def.catchall;for(const h of o){const g=r.data[h];c.push({key:{status:"valid",value:h},value:u._parse(new ce(r,g,r.path,h)),alwaysSet:h in r.data})}}return r.common.async?Promise.resolve().then(()=>R(this,null,function*(){const u=[];for(const h of c){const g=yield h.key,_=yield h.value;u.push({key:g,value:_,alwaysSet:h.alwaysSet})}return u})).then(u=>W.mergeObjectSync(n,u)):W.mergeObjectSync(n,c)}get shape(){return this._def.shape()}strict(e){return v.errToObj,new V(l(x(l({},this._def),{unknownKeys:"strict"}),e!==void 0?{errorMap:(t,n)=>{var a,i,o,c;const r=(o=(i=(a=this._def).errorMap)==null?void 0:i.call(a,t,n).message)!=null?o:n.defaultError;return t.code==="unrecognized_keys"?{message:(c=v.errToObj(e).message)!=null?c:r}:{message:r}}}:{}))}strip(){return new V(x(l({},this._def),{unknownKeys:"strip"}))}passthrough(){return new V(x(l({},this._def),{unknownKeys:"passthrough"}))}extend(e){return new V(x(l({},this._def),{shape:()=>l(l({},this._def.shape()),e)}))}merge(e){return new V({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>l(l({},this._def.shape()),e._def.shape()),typeName:b.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new V(x(l({},this._def),{catchall:e}))}pick(e){const t={};for(const n of N.objectKeys(e))e[n]&&this.shape[n]&&(t[n]=this.shape[n]);return new V(x(l({},this._def),{shape:()=>t}))}omit(e){const t={};for(const n of N.objectKeys(this.shape))e[n]||(t[n]=this.shape[n]);return new V(x(l({},this._def),{shape:()=>t}))}deepPartial(){return Ne(this)}partial(e){const t={};for(const n of N.objectKeys(this.shape)){const r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}return new V(x(l({},this._def),{shape:()=>t}))}required(e){const t={};for(const n of N.objectKeys(this.shape))if(e&&!e[n])t[n]=this.shape[n];else{let a=this.shape[n];for(;a instanceof fe;)a=a._def.innerType;t[n]=a}return new V(x(l({},this._def),{shape:()=>t}))}keyof(){return ws(N.objectKeys(this.shape))}}V.create=(s,e)=>new V(l({shape:()=>s,unknownKeys:"strip",catchall:ke.create(),typeName:b.ZodObject},w(e)));V.strictCreate=(s,e)=>new V(l({shape:()=>s,unknownKeys:"strict",catchall:ke.create(),typeName:b.ZodObject},w(e)));V.lazycreate=(s,e)=>new V(l({shape:s,unknownKeys:"strip",catchall:ke.create(),typeName:b.ZodObject},w(e)));class Ee extends O{_parse(e){const{ctx:t}=this._processInputParams(e),n=this._def.options;function r(a){for(const o of a)if(o.result.status==="valid")return o.result;for(const o of a)if(o.result.status==="dirty")return t.common.issues.push(...o.ctx.common.issues),o.result;const i=a.map(o=>new he(o.ctx.common.issues));return m(t,{code:d.invalid_union,unionErrors:i}),k}if(t.common.async)return Promise.all(n.map(a=>R(this,null,function*(){const i=x(l({},t),{common:x(l({},t.common),{issues:[]}),parent:null});return{result:yield a._parseAsync({data:t.data,path:t.path,parent:i}),ctx:i}}))).then(r);{let a;const i=[];for(const c of n){const u=x(l({},t),{common:x(l({},t.common),{issues:[]}),parent:null}),h=c._parseSync({data:t.data,path:t.path,parent:u});if(h.status==="valid")return h;h.status==="dirty"&&!a&&(a={result:h,ctx:u}),u.common.issues.length&&i.push(u.common.issues)}if(a)return t.common.issues.push(...a.ctx.common.issues),a.result;const o=i.map(c=>new he(c));return m(t,{code:d.invalid_union,unionErrors:o}),k}}get options(){return this._def.options}}Ee.create=(s,e)=>new Ee(l({options:s,typeName:b.ZodUnion},w(e)));function wt(s,e){const t=ge(s),n=ge(e);if(s===e)return{valid:!0,data:s};if(t===y.object&&n===y.object){const r=N.objectKeys(e),a=N.objectKeys(s).filter(o=>r.indexOf(o)!==-1),i=l(l({},s),e);for(const o of a){const c=wt(s[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}else if(t===y.array&&n===y.array){if(s.length!==e.length)return{valid:!1};const r=[];for(let a=0;a<s.length;a++){const i=s[a],o=e[a],c=wt(i,o);if(!c.valid)return{valid:!1};r.push(c.data)}return{valid:!0,data:r}}else return t===y.date&&n===y.date&&+s==+e?{valid:!0,data:s}:{valid:!1}}class Te extends O{_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=(a,i)=>{if(Wt(a)||Wt(i))return k;const o=wt(a.value,i.value);return o.valid?((Ht(a)||Ht(i))&&t.dirty(),{status:t.value,value:o.data}):(m(n,{code:d.invalid_intersection_types}),k)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([a,i])=>r(a,i)):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}Te.create=(s,e,t)=>new Te(l({left:s,right:e,typeName:b.ZodIntersection},w(t)));class xe extends O{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==y.array)return m(n,{code:d.invalid_type,expected:y.array,received:n.parsedType}),k;if(n.data.length<this._def.items.length)return m(n,{code:d.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),k;!this._def.rest&&n.data.length>this._def.items.length&&(m(n,{code:d.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const a=[...n.data].map((i,o)=>{const c=this._def.items[o]||this._def.rest;return c?c._parse(new ce(n,i,n.path,o)):null}).filter(i=>!!i);return n.common.async?Promise.all(a).then(i=>W.mergeArray(t,i)):W.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new xe(x(l({},this._def),{rest:e}))}}xe.create=(s,e)=>{if(!Array.isArray(s))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new xe(l({items:s,typeName:b.ZodTuple,rest:null},w(e)))};class Re extends O{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==y.object)return m(n,{code:d.invalid_type,expected:y.object,received:n.parsedType}),k;const r=[],a=this._def.keyType,i=this._def.valueType;for(const o in n.data)r.push({key:a._parse(new ce(n,o,n.path,o)),value:i._parse(new ce(n,n.data[o],n.path,o)),alwaysSet:o in n.data});return n.common.async?W.mergeObjectAsync(t,r):W.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,n){return t instanceof O?new Re(l({keyType:e,valueType:t,typeName:b.ZodRecord},w(n))):new Re(l({keyType:ae.create(),valueType:e,typeName:b.ZodRecord},w(t)))}}class ts extends O{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==y.map)return m(n,{code:d.invalid_type,expected:y.map,received:n.parsedType}),k;const r=this._def.keyType,a=this._def.valueType,i=[...n.data.entries()].map(([o,c],u)=>({key:r._parse(new ce(n,o,n.path,[u,"key"])),value:a._parse(new ce(n,c,n.path,[u,"value"]))}));if(n.common.async){const o=new Map;return Promise.resolve().then(()=>R(this,null,function*(){for(const c of i){const u=yield c.key,h=yield c.value;if(u.status==="aborted"||h.status==="aborted")return k;(u.status==="dirty"||h.status==="dirty")&&t.dirty(),o.set(u.value,h.value)}return{status:t.value,value:o}}))}else{const o=new Map;for(const c of i){const u=c.key,h=c.value;if(u.status==="aborted"||h.status==="aborted")return k;(u.status==="dirty"||h.status==="dirty")&&t.dirty(),o.set(u.value,h.value)}return{status:t.value,value:o}}}}ts.create=(s,e,t)=>new ts(l({valueType:e,keyType:s,typeName:b.ZodMap},w(t)));class Je extends O{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==y.set)return m(n,{code:d.invalid_type,expected:y.set,received:n.parsedType}),k;const r=this._def;r.minSize!==null&&n.data.size<r.minSize.value&&(m(n,{code:d.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),r.maxSize!==null&&n.data.size>r.maxSize.value&&(m(n,{code:d.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const a=this._def.valueType;function i(c){const u=new Set;for(const h of c){if(h.status==="aborted")return k;h.status==="dirty"&&t.dirty(),u.add(h.value)}return{status:t.value,value:u}}const o=[...n.data.values()].map((c,u)=>a._parse(new ce(n,c,n.path,u)));return n.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new Je(x(l({},this._def),{minSize:{value:e,message:v.toString(t)}}))}max(e,t){return new Je(x(l({},this._def),{maxSize:{value:e,message:v.toString(t)}}))}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}Je.create=(s,e)=>new Je(l({valueType:s,minSize:null,maxSize:null,typeName:b.ZodSet},w(e)));class ss extends O{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}ss.create=(s,e)=>new ss(l({getter:s,typeName:b.ZodLazy},w(e)));class ns extends O{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return m(t,{received:t.data,code:d.invalid_literal,expected:this._def.value}),k}return{status:"valid",value:e.data}}get value(){return this._def.value}}ns.create=(s,e)=>new ns(l({value:s,typeName:b.ZodLiteral},w(e)));function ws(s,e){return new $e(l({values:s,typeName:b.ZodEnum},w(e)))}class $e extends O{_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),n=this._def.values;return m(t,{expected:N.joinValues(n),received:t.parsedType,code:d.invalid_type}),k}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return m(t,{received:t.data,code:d.invalid_enum_value,options:n}),k}return ee(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return $e.create(e,l(l({},this._def),t))}exclude(e,t=this._def){return $e.create(this.options.filter(n=>!e.includes(n)),l(l({},this._def),t))}}$e.create=ws;class rs extends O{_parse(e){const t=N.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==y.string&&n.parsedType!==y.number){const r=N.objectValues(t);return m(n,{expected:N.joinValues(r),received:n.parsedType,code:d.invalid_type}),k}if(this._cache||(this._cache=new Set(N.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const r=N.objectValues(t);return m(n,{received:n.data,code:d.invalid_enum_value,options:r}),k}return ee(e.data)}get enum(){return this._def.values}}rs.create=(s,e)=>new rs(l({values:s,typeName:b.ZodNativeEnum},w(e)));class ct extends O{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==y.promise&&t.common.async===!1)return m(t,{code:d.invalid_type,expected:y.promise,received:t.parsedType}),k;const n=t.parsedType===y.promise?t.data:Promise.resolve(t.data);return ee(n.then(r=>this._def.type.parseAsync(r,{path:t.path,errorMap:t.common.contextualErrorMap})))}}ct.create=(s,e)=>new ct(l({type:s,typeName:b.ZodPromise},w(e)));class oe extends O{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===b.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=this._def.effect||null,a={addIssue:i=>{m(n,i),i.fatal?t.abort():t.dirty()},get path(){return n.path}};if(a.addIssue=a.addIssue.bind(a),r.type==="preprocess"){const i=r.transform(n.data,a);if(n.common.async)return Promise.resolve(i).then(o=>R(this,null,function*(){if(t.value==="aborted")return k;const c=yield this._def.schema._parseAsync({data:o,path:n.path,parent:n});return c.status==="aborted"?k:c.status==="dirty"||t.value==="dirty"?Ue(c.value):c}));{if(t.value==="aborted")return k;const o=this._def.schema._parseSync({data:i,path:n.path,parent:n});return o.status==="aborted"?k:o.status==="dirty"||t.value==="dirty"?Ue(o.value):o}}if(r.type==="refinement"){const i=o=>{const c=r.refinement(o,a);if(n.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(n.common.async===!1){const o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return o.status==="aborted"?k:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(o=>o.status==="aborted"?k:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(r.type==="transform")if(n.common.async===!1){const i=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Ie(i))return k;const o=r.transform(i.value,a);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(i=>Ie(i)?Promise.resolve(r.transform(i.value,a)).then(o=>({status:t.value,value:o})):k);N.assertNever(r)}}oe.create=(s,e,t)=>new oe(l({schema:s,typeName:b.ZodEffects,effect:e},w(t)));oe.createWithPreprocess=(s,e,t)=>new oe(l({schema:e,effect:{type:"preprocess",transform:s},typeName:b.ZodEffects},w(t)));class fe extends O{_parse(e){return this._getType(e)===y.undefined?ee(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}fe.create=(s,e)=>new fe(l({innerType:s,typeName:b.ZodOptional},w(e)));class je extends O{_parse(e){return this._getType(e)===y.null?ee(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}je.create=(s,e)=>new je(l({innerType:s,typeName:b.ZodNullable},w(e)));class Ze extends O{_parse(e){const{ctx:t}=this._processInputParams(e);let n=t.data;return t.parsedType===y.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Ze.create=(s,e)=>new Ze(l({innerType:s,typeName:b.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default},w(e)));class Ct extends O{_parse(e){const{ctx:t}=this._processInputParams(e),n=x(l({},t),{common:x(l({},t.common),{issues:[]})}),r=this._def.innerType._parse({data:n.data,path:n.path,parent:l({},n)});return it(r)?r.then(a=>({status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new he(n.common.issues)},input:n.data})})):{status:"valid",value:r.status==="valid"?r.value:this._def.catchValue({get error(){return new he(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}Ct.create=(s,e)=>new Ct(l({innerType:s,typeName:b.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch},w(e)));class as extends O{_parse(e){if(this._getType(e)!==y.nan){const n=this._getOrReturnCtx(e);return m(n,{code:d.invalid_type,expected:y.nan,received:n.parsedType}),k}return{status:"valid",value:e.data}}}as.create=s=>new as(l({typeName:b.ZodNaN},w(s)));class fr extends O{_parse(e){const{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class Vt extends O{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return R(this,null,function*(){const a=yield this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return a.status==="aborted"?k:a.status==="dirty"?(t.dirty(),Ue(a.value)):this._def.out._parseAsync({data:a.value,path:n.path,parent:n})});{const r=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?k:r.status==="dirty"?(t.dirty(),{status:"dirty",value:r.value}):this._def.out._parseSync({data:r.value,path:n.path,parent:n})}}static create(e,t){return new Vt({in:e,out:t,typeName:b.ZodPipeline})}}class St extends O{_parse(e){const t=this._def.innerType._parse(e),n=r=>(Ie(r)&&(r.value=Object.freeze(r.value)),r);return it(t)?t.then(r=>n(r)):n(t)}unwrap(){return this._def.innerType}}St.create=(s,e)=>new St(l({innerType:s,typeName:b.ZodReadonly},w(e)));V.lazycreate;var b;(function(s){s.ZodString="ZodString",s.ZodNumber="ZodNumber",s.ZodNaN="ZodNaN",s.ZodBigInt="ZodBigInt",s.ZodBoolean="ZodBoolean",s.ZodDate="ZodDate",s.ZodSymbol="ZodSymbol",s.ZodUndefined="ZodUndefined",s.ZodNull="ZodNull",s.ZodAny="ZodAny",s.ZodUnknown="ZodUnknown",s.ZodNever="ZodNever",s.ZodVoid="ZodVoid",s.ZodArray="ZodArray",s.ZodObject="ZodObject",s.ZodUnion="ZodUnion",s.ZodDiscriminatedUnion="ZodDiscriminatedUnion",s.ZodIntersection="ZodIntersection",s.ZodTuple="ZodTuple",s.ZodRecord="ZodRecord",s.ZodMap="ZodMap",s.ZodSet="ZodSet",s.ZodFunction="ZodFunction",s.ZodLazy="ZodLazy",s.ZodLiteral="ZodLiteral",s.ZodEnum="ZodEnum",s.ZodEffects="ZodEffects",s.ZodNativeEnum="ZodNativeEnum",s.ZodOptional="ZodOptional",s.ZodNullable="ZodNullable",s.ZodDefault="ZodDefault",s.ZodCatch="ZodCatch",s.ZodPromise="ZodPromise",s.ZodBranded="ZodBranded",s.ZodPipeline="ZodPipeline",s.ZodReadonly="ZodReadonly"})(b||(b={}));const zr=ae.create,Ur=ot.create;ke.create;ie.create;const hr=V.create;V.strictCreate;Ee.create;Te.create;xe.create;Re.create;$e.create;ct.create;oe.create;fe.create;je.create;oe.createWithPreprocess;const Ce=(s,e)=>s.constructor.name===e.name,z=new Map;z.set(ot.name,()=>!1),z.set(Oe.name,()=>0),z.set(ae.name,()=>""),z.set(ie.name,()=>[]),z.set(Re.name,()=>({})),z.set(Ze.name,s=>s._def.defaultValue()),z.set(oe.name,s=>qe(s._def.schema)),z.set(fe.name,s=>Ce(s._def.innerType,Ze)?s._def.innerType._def.defaultValue():void 0),z.set(xe.name,s=>{const e=[];for(const t of s._def.items)e.push(qe(t));return e}),z.set(oe.name,s=>qe(s._def.schema)),z.set(Ee.name,s=>qe(s._def.options[0])),z.set(V.name,s=>_e(s)),z.set(Re.name,s=>_e(s)),z.set(Te.name,s=>_e(s));function qe(s){const e=s.constructor.name;if(!z.has(e)){console.warn("getSchemaDefaultForField: Unhandled type",s.constructor.name);return}return z.get(e)(s)}function _e(s){if(Ce(s,Re))return{};if(Ce(s,oe))return _e(s._def.schema);if(Ce(s,Te))return l(l({},_e(s._def.left)),_e(s._def.right));if(Ce(s,Ee)){for(const e of s._def.options)if(Ce(e,V))return _e(e);return console.warn("getSchemaDefaultObject: No object found in union, returning empty object"),{}}return Ce(s,V)?Object.fromEntries(Object.entries(s.shape).map(([e,t])=>[e,qe(t)]).filter(e=>e[1]!==void 0)):(console.warn(`getSchemaDefaultObject: Expected object schema, got ${s.constructor.name}`),{})}function mr(s){return _e(s)}const[pr,yr]=Nt("VbenFormProps"),[vr,gr]=Nt("ComponentRefMap");function _r(s){var o;const e=zs(),t=a(),n=dn(l({},(o=Object.keys(t))!=null&&o.length?{initialValues:t}:{})),r=E(()=>{const c=[];for(const u of Object.keys(e))u!=="default"&&c.push(u);return c});function a(){const c={},u={};(f(s).schema||[]).forEach(_=>{if(Reflect.has(_,"defaultValue"))_t(c,_.fieldName,_.defaultValue);else if(_.rules&&!de(_.rules)){const S=i(_.rules);u[_.fieldName]=_.rules,S!==void 0&&(c[_.fieldName]=S)}});const h=mr(hr(u)),g={};for(const _ in h)_t(g,_,h[_]);return We(c,g)}function i(c){if(c instanceof ae)return"";if(c instanceof Oe)return null;if(c instanceof V){const u={};for(const[h,g]of Object.entries(c.shape))u[h]=i(g);return u}else if(c instanceof Te){const u=i(c._def.left),h=i(c._def.right);return typeof u=="object"&&typeof h=="object"?l(l({},u),h):u!=null?u:h}else return}return{delegatedSlots:r,form:n}}const br=G({__name:"form-actions",props:{modelValue:{default:!1},modelModifiers:{}},emits:["update:modelValue"],setup(s,{expose:e}){const{$t:t}=fn(),[n,r]=pr(),a=us(s,"modelValue"),i=E(()=>l({content:`${t.value("reset")}`,show:!0},f(n).resetButtonOptions)),o=E(()=>l({content:`${t.value("submit")}`,show:!0},f(n).submitButtonOptions)),c=E(()=>f(n).actionWrapperClass?{}:{"grid-column":"-2 / -1",marginLeft:"auto"});function u(g){return R(this,null,function*(){var j,C,T;g==null||g.preventDefault(),g==null||g.stopPropagation();const{valid:_}=yield r.validate();if(!_)return;const S=Se(yield(j=f(n).formApi)==null?void 0:j.getValues());yield(T=(C=f(n)).handleSubmit)==null?void 0:T.call(C,S)})}function h(g){return R(this,null,function*(){var j,C;g==null||g.preventDefault(),g==null||g.stopPropagation();const _=f(n),S=Se(yield(j=_.formApi)==null?void 0:j.getValues());J(_.handleReset)?yield(C=_.handleReset)==null?void 0:C.call(_,S):r.resetForm()})}return be(()=>a.value,()=>{f(n).collapseTriggerResize&&Us()}),e({handleReset:h,handleSubmit:u}),(g,_)=>(A(),le("div",{class:Q(f(q)("col-span-full w-full text-right",f(n).compact?"pb-2":"pb-6",f(n).actionWrapperClass)),style:ds(c.value)},[f(n).actionButtonsReverse?(A(),le(bt,{key:0},[Z(g.$slots,"submit-before"),o.value.show?(A(),P(He(f(st).PrimaryButton),re({key:0,class:"ml-3",type:"button",onClick:u},o.value),{default:F(()=>[tt(Le(o.value.content),1)]),_:1},16)):B("",!0)],64)):B("",!0),Z(g.$slots,"reset-before"),i.value.show?(A(),P(He(f(st).DefaultButton),re({key:1,class:"ml-3",type:"button",onClick:h},i.value),{default:F(()=>[tt(Le(i.value.content),1)]),_:1},16)):B("",!0),f(n).actionButtonsReverse?B("",!0):(A(),le(bt,{key:2},[Z(g.$slots,"submit-before"),o.value.show?(A(),P(He(f(st).PrimaryButton),re({key:0,class:"ml-3",type:"button",onClick:u},o.value),{default:F(()=>[tt(Le(o.value.content),1)]),_:1},16)):B("",!0)],64)),Z(g.$slots,"expand-before"),f(n).showCollapseButton?(A(),P(f(Fn),{key:3,"model-value":a.value,"onUpdate:modelValue":_[0]||(_[0]=S=>a.value=S),class:"ml-2"},{default:F(()=>[Ge("span",null,Le(a.value?f(t)("expand"):f(t)("collapse")),1)]),_:1},8,["model-value"])):B("",!0),Z(g.$slots,"expand-after")],6))}});const is=s=>s!==null&&!!s&&typeof s=="object"&&!Array.isArray(s);function Cs(s){return Number(s)>=0}function kr(s){return typeof s=="object"&&s!==null}function xr(s){return s==null?s===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(s)}function os(s){if(!kr(s)||xr(s)!=="[object Object]")return!1;if(Object.getPrototypeOf(s)===null)return!0;let e=s;for(;Object.getPrototypeOf(e)!==null;)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(s)===e}function Ss(s,e){return Object.keys(e).forEach(t=>{if(os(e[t])&&os(s[t])){s[t]||(s[t]={}),Ss(s[t],e[t]);return}s[t]=e[t]}),s}function wr(s){const e=s.split(".");if(!e.length)return"";let t=String(e[0]);for(let n=1;n<e.length;n++){if(Cs(e[n])){t+=`[${e[n]}]`;continue}t+=`.${e[n]}`}return t}function Cr(s,e){return{__type:"VVTypedSchema",parse(r){return R(this,null,function*(){const a=yield s.safeParseAsync(r,e);if(a.success)return{value:a.data,errors:[]};const i={};return Os(a.error.issues,i),{errors:Object.values(i)}})},cast(r){try{return s.parse(r)}catch(a){const i=Ts(s);return is(i)&&is(r)?Ss(i,r):r}},describe(r){try{if(!r)return{required:!s.isOptional(),exists:!0};const a=Sr(r,s);return a?{required:!a.isOptional(),exists:!0}:{required:!1,exists:!1}}catch(a){return{required:!1,exists:!1}}}}}function Os(s,e){s.forEach(t=>{const n=wr(t.path.join("."));t.code==="invalid_union"&&(Os(t.unionErrors.flatMap(r=>r.issues),e),!n)||(e[n]||(e[n]={errors:[],path:n}),e[n].errors.push(t.message))})}function Ts(s){if(s instanceof V)return Object.fromEntries(Object.entries(s.shape).map(([e,t])=>t instanceof Ze?[e,t._def.defaultValue()]:t instanceof V?[e,Ts(t)]:[e,void 0]))}function Sr(s,e){if(!ls(e))return null;if(hn(s))return e.shape[mn(s)];const t=(s||"").split(/\.|\[(\d+)\]/).filter(Boolean);let n=e;for(let r=0;r<=t.length;r++){const a=t[r];if(!a||!n)return n;if(ls(n)){n=n.shape[a]||null;continue}Cs(a)&&Or(n)&&(n=n._def.type)}return null}function Rs(s){return s._def.typeName}function Or(s){return Rs(s)===b.ZodArray}function ls(s){return Rs(s)===b.ZodObject}const[Ft,Tr]=Nt("FormRenderProps"),Rr=()=>{const s=Ft(),e=E(()=>s.layout==="vertical"),t=E(()=>s.componentMap);return{componentBindEventMap:E(()=>s.componentBindEventMap),componentMap:t,isVertical:e}};function Ar(s){const e=gs(),n=Ft().form;if(!e)throw new Error("useDependencies should be used within <VbenForm>");const r=ue(!0),a=ue(!1),i=ue(!0),o=ue(!1),c=ue({}),u=ue(),h=E(()=>{var S,j;return((j=(S=s())==null?void 0:S.triggerFields)!=null?j:[]).map(C=>e.value[C])}),g=()=>{a.value=!1,r.value=!0,i.value=!0,o.value=!1,u.value=void 0,c.value={}};return be([h,s],j=>R(null,[j],function*([_,S]){var K;if(!S||!((K=S==null?void 0:S.triggerFields)!=null&&K.length))return;g();const{componentProps:C,disabled:T,if:M,required:I,rules:$,show:H,trigger:X}=S,te=e.value;if(J(M)){if(r.value=!!(yield M(te,n)),!r.value)return}else if(yt(M)&&(r.value=M,!r.value))return;if(J(H)){if(i.value=!!(yield H(te,n)),!i.value)return}else if(yt(H)&&(i.value=H,!i.value))return;J(C)&&(c.value=yield C(te,n)),J($)&&(u.value=yield $(te,n)),J(T)?a.value=!!(yield T(te,n)):yt(T)&&(a.value=T),J(I)&&(o.value=!!(yield I(te,n))),J(X)&&(yield X(te,n))}),{deep:!0,immediate:!0}),{dynamicComponentProps:c,dynamicRules:u,isDisabled:a,isIf:r,isRequired:o,isShow:i}}const Nr={key:0,class:"text-destructive mr-[2px]"},Vr={key:2,class:"ml-[2px]"},Fr=G({__name:"form-label",props:{class:{},colon:{type:Boolean},help:{type:[Function,String]},label:{type:[Function,String]},required:{type:Boolean}},setup(s){const e=s;return(t,n)=>(A(),P(f(Nn),{class:Q(f(q)("flex items-center",e.class))},{default:F(()=>[t.required?(A(),le("span",Nr,"*")):B("",!0),Z(t.$slots,"default"),t.help?(A(),P(f(Vn),{key:1,"trigger-class":"size-3.5 ml-1"},{default:F(()=>[ne(f(ze),{content:t.help},null,8,["content"])]),_:1})):B("",!0),t.colon&&t.label?(A(),le("span",Vr,":")):B("",!0)]),_:3},8,["class"]))}});function Ot(s){return!s||de(s)?null:"innerType"in s._def?Ot(s._def.innerType):"schema"in s._def?Ot(s._def.schema):s}function Tt(s){if(!s||de(s))return;const e=s;if(e._def.typeName==="ZodDefault")return e._def.defaultValue();if("innerType"in e._def)return Tt(e._def.innerType);if("schema"in e._def)return Tt(e._def.schema)}function cs(s){return!s||!Rt(s)?!1:Reflect.has(s,"target")&&Reflect.has(s,"stopPropagation")}const Ir={class:"flex-auto overflow-hidden p-[1px]"},Er={key:0,class:"ml-1"},$r=G({__name:"form-field",props:{component:{},componentProps:{type:Function},defaultValue:{},dependencies:{},description:{type:[Function,String]},fieldName:{},help:{type:[Function,String]},label:{type:[Function,String]},renderComponentContent:{type:Function},rules:{},suffix:{type:[Function,String]},colon:{type:Boolean},controlClass:{},disabled:{type:Boolean},disabledOnChangeListener:{type:Boolean},disabledOnInputListener:{type:Boolean},emptyStateValue:{},formFieldProps:{},formItemClass:{},hideLabel:{type:Boolean},hideRequiredMark:{type:Boolean},labelClass:{},labelWidth:{},modelPropName:{},wrapperClass:{},commonComponentProps:{}},setup(s){const{componentBindEventMap:e,componentMap:t,isVertical:n}=Rr(),r=Ft(),a=gs(),i=ys(s.fieldName),o=fs("fieldComponentRef"),c=r.form,u=r.compact,h=E(()=>{var p;return((p=i.value)==null?void 0:p.length)>0}),g=E(()=>{const p=de(s.component)?t.value[s.component]:s.component;return p||console.warn(`Component ${s.component} is not registered`),p}),{dynamicComponentProps:_,dynamicRules:S,isDisabled:j,isIf:C,isRequired:T,isShow:M}=Ar(()=>s.dependencies),I=E(()=>{var p;return(p=s.labelClass)!=null&&p.includes("w-")||n.value?{}:{width:`${s.labelWidth}px`}}),$=E(()=>S.value||s.rules),H=E(()=>C.value&&M.value),X=E(()=>{var U,L,pe,Pe,De,Be;if(!H.value)return!1;if(!$.value)return T.value;if(T.value)return!0;if(de($.value))return["required","selectRequired"].includes($.value);let p=(L=(U=$==null?void 0:$.value)==null?void 0:U.isOptional)==null?void 0:L.call(U);if(((Pe=(pe=$==null?void 0:$.value)==null?void 0:pe._def)==null?void 0:Pe.typeName)==="ZodDefault"){const se=(De=$==null?void 0:$.value)==null?void 0:De._def.innerType;se&&(p=(Be=se.isOptional)==null?void 0:Be.call(se))}return!p}),te=E(()=>{var U;if(!H.value)return null;let p=$.value;if(!p)return T.value?"required":null;if(de(p))return p;if(!!X.value){const L=(U=p==null?void 0:p.unwrap)==null?void 0:U.call(p);L&&(p=L)}return Cr(p)}),K=E(()=>{const p=J(s.componentProps)?s.componentProps(a.value,c):s.componentProps;return l(l(l({},s.commonComponentProps),p),_.value)});be(()=>{var p;return(p=K.value)==null?void 0:p.autofocus},p=>{p===!0&&rt(()=>{ht()})},{immediate:!0});const Qe=E(()=>{var p;return j.value||s.disabled||((p=K.value)==null?void 0:p.disabled)}),Xe=E(()=>J(s.renderComponentContent)?s.renderComponentContent(a.value,c):{}),dt=E(()=>Object.keys(Xe.value)),me=E(()=>{const p=te.value;return l(l({keepValue:!0,label:de(s.label)?s.label:""},p?{rules:p}:{}),s.formFieldProps)});function ft(p){var Pe,De,Be;const D=p.componentField.modelValue,U=p.componentField["onUpdate:modelValue"],L=s.modelPropName||(de(s.component)?(Pe=e.value)==null?void 0:Pe[s.component]:null);let pe=D;return D&&Rt(D)&&L&&(pe=cs(D)?(De=D==null?void 0:D.target)==null?void 0:De[L]:(Be=D==null?void 0:D[L])!=null?Be:D),L?l({[`onUpdate:${L}`]:U,[L]:pe===void 0?s.emptyStateValue:pe,onChange:s.disabledOnChangeListener?void 0:se=>{var It,Et,$t;const As=cs(se),Ae=(It=p==null?void 0:p.componentField)==null?void 0:It.onChange;return As?Ae==null?void 0:Ae(($t=(Et=se==null?void 0:se.target)==null?void 0:Et[L])!=null?$t:se):Ae==null?void 0:Ae(se)}},s.disabledOnInputListener?{onInput:void 0}:{}):l(l({},s.disabledOnInputListener?{onInput:void 0}:{}),s.disabledOnChangeListener?{onChange:void 0}:{})}function Me(p){const D=ft(p);return l(l(l(l(l({},p.componentField),K.value),D),Reflect.has(K.value,"onChange")?{onChange:K.value.onChange}:{}),Reflect.has(K.value,"onInput")?{onInput:K.value.onInput}:{})}function ht(){var p,D;o.value&&J(o.value.focus)&&document.activeElement!==o.value&&((D=(p=o.value)==null?void 0:p.focus)==null||D.call(p))}const we=vr();return be(o,p=>{we==null||we.set(s.fieldName,p)}),qs(()=>{we!=null&&we.has(s.fieldName)&&we.delete(s.fieldName)}),(p,D)=>f(C)?(A(),P(f(pn),re({key:0},me.value,{name:p.fieldName}),{default:F(U=>[Ws(ne(f(Rn),re({class:[{"form-valid-error":h.value,"form-is-required":X.value,"flex-col":f(n),"flex-row items-center":!f(n),"pb-6":!f(u),"pb-2":f(u)},"relative flex"]},p.$attrs),{default:F(()=>[p.hideLabel?B("",!0):(A(),P(Fr,{key:0,class:Q(f(q)("flex leading-6",{"mr-2 flex-shrink-0 justify-end":!f(n),"mb-1 flex-row":f(n)},p.labelClass)),help:p.help,colon:p.colon,label:p.label,required:X.value&&!p.hideRequiredMark,style:ds(I.value)},{default:F(()=>[p.label?(A(),P(f(ze),{key:0,content:p.label},null,8,["content"])):B("",!0)]),_:1},8,["class","help","colon","label","required","style"])),Ge("div",Ir,[Ge("div",{class:Q(f(q)("relative flex w-full items-center",p.wrapperClass))},[ne(f(Sn),{class:Q(f(q)(p.controlClass))},{default:F(()=>[Z(p.$slots,"default",ye(ve(x(l(l({},U),Me(U)),{disabled:Qe.value,isInValid:h.value}))),()=>[(A(),P(He(g.value),re({ref_key:"fieldComponentRef",ref:o,class:{"border-destructive focus:border-destructive hover:border-destructive/80 focus:shadow-[0_0_0_2px_rgba(255,38,5,0.06)]":h.value}},Me(U),{disabled:Qe.value}),hs({_:2},[At(dt.value,L=>({name:L,fn:F(pe=>[ne(f(ze),re({content:Xe.value[L]},x(l({},pe),{formContext:U})),null,16,["content"])])}))]),1040,["class","disabled"])),f(u)&&h.value?(A(),P(f(vs),{key:0,"delay-duration":300,side:"left"},{trigger:F(()=>[Z(p.$slots,"trigger",{},()=>[ne(f(wn),{class:Q(f(q)("text-foreground/80 hover:text-foreground inline-flex size-5 cursor-pointer"))},null,8,["class"])])]),default:F(()=>[ne(f(zt))]),_:3})):B("",!0)])]),_:2},1032,["class"]),p.suffix?(A(),le("div",Er,[ne(f(ze),{content:p.suffix},null,8,["content"])])):B("",!0),p.description?(A(),P(f(Tn),{key:1,class:"ml-1"},{default:F(()=>[ne(f(ze),{content:p.description},null,8,["content"])]),_:1})):B("",!0)],2),f(u)?B("",!0):(A(),P(vn,{key:0,name:"slide-up"},{default:F(()=>[ne(f(zt),{class:"absolute bottom-1"})]),_:1}))])]),_:2},1040,["class"]),[[yn,f(M)]])]),_:3},16,["name"])):B("",!0)}});function jr(s){const e=fs("wrapperRef"),t=Hs(e),n=ue({}),r=ue(!1),a=Gs(Ys),i=E(()=>{var g,_;const c=(g=s.collapsedRows)!=null?g:1,u=n.value;let h=0;for(let S=1;S<=c;S++)h+=(_=u==null?void 0:u[S])!=null?_:0;return h-1||1});be([()=>s.showCollapseButton,()=>a.active().value,()=>{var c;return(c=s.schema)==null?void 0:c.length},()=>t.value],u=>R(null,[u],function*([c]){c&&(yield rt(),n.value={},r.value=!1,yield o())}));function o(){return R(this,null,function*(){if(!s.showCollapseButton||(yield rt(),!e.value))return;const c=[...e.value.children],u=e.value,g=window.getComputedStyle(u).getPropertyValue("grid-template-rows").split(" "),_=u==null?void 0:u.getBoundingClientRect();c.forEach(S=>{var I,$;const C=S.getBoundingClientRect().top-_.top;let T=0,M=0;for(const[H,X]of g.entries())if(M+=Number.parseFloat(X),C<M){T=H+1;break}T>((I=s==null?void 0:s.collapsedRows)!=null?I:1)||(n.value[T]=(($=n.value[T])!=null?$:0)+1,r.value=!0)})})}return ms(()=>{o()}),{isCalculated:r,keepFormItemIndex:i,wrapperRef:e}}const Zr=G({__name:"form",props:{arrayToStringFields:{},collapsed:{type:Boolean},collapsedRows:{default:1},collapseTriggerResize:{type:Boolean},commonConfig:{default:()=>({})},compact:{type:Boolean},componentBindEventMap:{},componentMap:{},fieldMappingTime:{},form:{},layout:{},schema:{},showCollapseButton:{type:Boolean,default:!1},wrapperClass:{default:"grid-cols-1 sm:grid-cols-2 md:grid-cols-3"},globalCommonConfig:{default:()=>({})}},emits:["submit"],setup(s,{emit:e}){const t=s,n=e;Tr(t);const{isCalculated:r,keepFormItemIndex:a,wrapperRef:i}=jr(t),o=E(()=>{var S;const _=[];return(S=t.schema)==null||S.forEach(j=>{const{fieldName:C}=j,T=j.rules;let M="";T&&!de(T)&&(M=T._def.typeName);const I=Ot(T);_.push({default:Tt(T),fieldName:C,required:!["ZodNullable","ZodOptional"].includes(M),rules:I})}),_}),c=E(()=>t.form?"form":gn),u=E(()=>t.form?{onSubmit:t.form.handleSubmit(_=>n("submit",_))}:{onSubmit:_=>n("submit",_)}),h=E(()=>t.collapsed&&r.value),g=E(()=>{const{colon:_=!1,componentProps:S={},controlClass:j="",disabled:C,disabledOnChangeListener:T=!0,disabledOnInputListener:M=!0,emptyStateValue:I=void 0,formFieldProps:$={},formItemClass:H="",hideLabel:X=!1,hideRequiredMark:te=!1,labelClass:K="",labelWidth:Qe=100,modelPropName:Xe="",wrapperClass:dt=""}=We(t.commonConfig,t.globalCommonConfig);return(t.schema||[]).map((me,ft)=>{const Me=a.value,ht=t.showCollapseButton&&h.value&&Me?Me<=ft:!1;return x(l({colon:_,disabled:C,disabledOnChangeListener:T,disabledOnInputListener:M,emptyStateValue:I,hideLabel:X,hideRequiredMark:te,labelWidth:Qe,modelPropName:Xe,wrapperClass:dt},me),{commonComponentProps:S,componentProps:me.componentProps,controlClass:q(j,me.controlClass),formFieldProps:l(l({},$),me.formFieldProps),formItemClass:q("flex-shrink-0",{hidden:ht},H,me.formItemClass),labelClass:q(K,me.labelClass)})})});return(_,S)=>(A(),P(He(c.value),ye(ve(u.value)),{default:F(()=>[Ge("div",{ref_key:"wrapperRef",ref:i,class:Q([_.wrapperClass,"grid"])},[(A(!0),le(bt,null,At(g.value,j=>(A(),P($r,re({key:j.fieldName,ref_for:!0},j,{class:j.formItemClass,rules:j.rules}),{default:F(C=>[Z(_.$slots,j.fieldName,re({ref_for:!0},C))]),_:2},1040,["class","rules"]))),128)),Z(_.$slots,"default",{shapes:o.value})],2)]),_:3},16))}}),Mr=G({__name:"vben-use-form",props:{formApi:{},actionButtonsReverse:{type:Boolean},actionWrapperClass:{},arrayToStringFields:{},fieldMappingTime:{},handleReset:{type:Function},handleSubmit:{type:Function},handleValuesChange:{type:Function},resetButtonOptions:{},showDefaultActions:{type:Boolean},submitButtonOptions:{},submitOnChange:{type:Boolean},submitOnEnter:{type:Boolean},collapsed:{type:Boolean},collapsedRows:{},collapseTriggerResize:{type:Boolean},commonConfig:{},compact:{type:Boolean},layout:{},schema:{},showCollapseButton:{type:Boolean},wrapperClass:{}},setup(s){var g,_,S,j;const e=s,t=(_=(g=e.formApi)==null?void 0:g.useStore)==null?void 0:_.call(g),n=_n(e,t),r=new Map,{delegatedSlots:a,form:i}=_r(n);yr([n,i]),gr(r),(j=(S=e.formApi)==null?void 0:S.mount)==null||j.call(S,i,r);const o=C=>{var T;(T=e.formApi)==null||T.setState({collapsed:!!C})};function c(C){var T;!t.value.submitOnEnter||!((T=n.value.formApi)!=null&&T.isMounted)||C.target instanceof HTMLTextAreaElement||(C.preventDefault(),n.value.formApi.validateAndSubmitForm())}const u=Xs(()=>R(null,null,function*(){var C;t.value.submitOnChange&&((C=n.value.formApi)==null||C.validateAndSubmitForm())}),300),h={};return ms(()=>R(null,null,function*(){yield rt(),be(()=>i.values,C=>R(null,null,function*(){var T;if(n.value.handleValuesChange){const M=(T=t.value.schema)==null?void 0:T.map(I=>I.fieldName);if(M&&M.length>0){const I=[];M.forEach($=>{const H=Lt(C,$),X=Lt(h,$);Js(H,X)||(I.push($),_t(h,$,H))}),I.length>0&&n.value.handleValuesChange(Qs(yield n.value.formApi.getValues()),I)}}u()}),{deep:!0})})),(C,T)=>(A(),P(f(Zr),re({onKeydown:xn(c,["enter"])},f(n),{collapsed:f(t).collapsed,"component-bind-event-map":f(kn),"component-map":f(st),form:f(i),"global-common-config":f(bn)}),hs({default:F(M=>[Z(C.$slots,"default",ye(ve(M)),()=>[f(n).showDefaultActions?(A(),P(br,{key:0,"model-value":f(t).collapsed,"onUpdate:modelValue":o},{"reset-before":F(I=>[Z(C.$slots,"reset-before",ye(ve(I)))]),"submit-before":F(I=>[Z(C.$slots,"submit-before",ye(ve(I)))]),"expand-before":F(I=>[Z(C.$slots,"expand-before",ye(ve(I)))]),"expand-after":F(I=>[Z(C.$slots,"expand-after",ye(ve(I)))]),_:3},8,["model-value"])):B("",!0)])]),_:2},[At(f(a),M=>({name:M,fn:F(I=>[Z(C.$slots,M,ye(ve(I)))])}))]),1040,["collapsed","component-bind-event-map","component-map","form","global-common-config"]))}});function qr(s){const e=Ks(s),t=new Dn(s),n=t;n.useStore=a=>Zn(t.store,a);const r=G((a,{attrs:i,slots:o})=>(en(()=>{t.unmount()}),t.setState(l(l({},a),i)),()=>tn(Mr,x(l(l({},a),i),{formApi:n}),o)),{name:"VbenUseForm",inheritAttrs:!1});return e&&be(()=>s.schema,()=>{t.setState({schema:s.schema})},{immediate:!0}),[r,n]}export{Cn as C,kt as S,Vn as _,Zn as a,Ur as b,zr as s,qr as u};
