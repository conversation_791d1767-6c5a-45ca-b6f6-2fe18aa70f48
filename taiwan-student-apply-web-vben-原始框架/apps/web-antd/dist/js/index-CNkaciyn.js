import{k as F,m as L,_ as w,r as V,V as _,v as O,P as c,l as W,h as A,a as X,W as R,b as I,o as j,X as K,G as v,K as b}from"./bootstrap-CMNRQ0xm.js";import{u as N}from"./FormItemContext-DdkyQObd.js";import{be as U,a4 as G,J as M,aD as J,P as x,Y as Q,a9 as Y,n as q,x as o}from"../jse/index-index-DjeMElj0.js";const Z=i=>{const{componentCls:n}=i,e=`${n}-inner`;return{[n]:{[`&${n}-small`]:{minWidth:i.switchMinWidthSM,height:i.switchHeightSM,lineHeight:`${i.switchHeightSM}px`,[`${n}-inner`]:{paddingInlineStart:i.switchInnerMarginMaxSM,paddingInlineEnd:i.switchInnerMarginMinSM,[`${e}-checked`]:{marginInlineStart:`calc(-100% + ${i.switchPinSizeSM+i.switchPadding*2}px - ${i.switchInnerMarginMaxSM*2}px)`,marginInlineEnd:`calc(100% - ${i.switchPinSizeSM+i.switchPadding*2}px + ${i.switchInnerMarginMaxSM*2}px)`},[`${e}-unchecked`]:{marginTop:-i.switchHeightSM,marginInlineStart:0,marginInlineEnd:0}},[`${n}-handle`]:{width:i.switchPinSizeSM,height:i.switchPinSizeSM},[`${n}-loading-icon`]:{top:(i.switchPinSizeSM-i.switchLoadingIconSize)/2,fontSize:i.switchLoadingIconSize},[`&${n}-checked`]:{[`${n}-inner`]:{paddingInlineStart:i.switchInnerMarginMinSM,paddingInlineEnd:i.switchInnerMarginMaxSM,[`${e}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${e}-unchecked`]:{marginInlineStart:`calc(100% - ${i.switchPinSizeSM+i.switchPadding*2}px + ${i.switchInnerMarginMaxSM*2}px)`,marginInlineEnd:`calc(-100% + ${i.switchPinSizeSM+i.switchPadding*2}px - ${i.switchInnerMarginMaxSM*2}px)`}},[`${n}-handle`]:{insetInlineStart:`calc(100% - ${i.switchPinSizeSM+i.switchPadding}px)`}},[`&:not(${n}-disabled):active`]:{[`&:not(${n}-checked) ${e}`]:{[`${e}-unchecked`]:{marginInlineStart:i.marginXXS/2,marginInlineEnd:-i.marginXXS/2}},[`&${n}-checked ${e}`]:{[`${e}-checked`]:{marginInlineStart:-i.marginXXS/2,marginInlineEnd:i.marginXXS/2}}}}}}},k=i=>{const{componentCls:n}=i;return{[n]:{[`${n}-loading-icon${i.iconCls}`]:{position:"relative",top:(i.switchPinSize-i.fontSize)/2,color:i.switchLoadingIconColor,verticalAlign:"top"},[`&${n}-checked ${n}-loading-icon`]:{color:i.switchColor}}}},ii=i=>{const{componentCls:n}=i,e=`${n}-handle`;return{[n]:{[e]:{position:"absolute",top:i.switchPadding,insetInlineStart:i.switchPadding,width:i.switchPinSize,height:i.switchPinSize,transition:`all ${i.switchDuration} ease-in-out`,"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:i.colorWhite,borderRadius:i.switchPinSize/2,boxShadow:i.switchHandleShadow,transition:`all ${i.switchDuration} ease-in-out`,content:'""'}},[`&${n}-checked ${e}`]:{insetInlineStart:`calc(100% - ${i.switchPinSize+i.switchPadding}px)`},[`&:not(${n}-disabled):active`]:{[`${e}::before`]:{insetInlineEnd:i.switchHandleActiveInset,insetInlineStart:0},[`&${n}-checked ${e}::before`]:{insetInlineEnd:0,insetInlineStart:i.switchHandleActiveInset}}}}},ni=i=>{const{componentCls:n}=i,e=`${n}-inner`;return{[n]:{[e]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:i.switchInnerMarginMax,paddingInlineEnd:i.switchInnerMarginMin,transition:`padding-inline-start ${i.switchDuration} ease-in-out, padding-inline-end ${i.switchDuration} ease-in-out`,[`${e}-checked, ${e}-unchecked`]:{display:"block",color:i.colorTextLightSolid,fontSize:i.fontSizeSM,transition:`margin-inline-start ${i.switchDuration} ease-in-out, margin-inline-end ${i.switchDuration} ease-in-out`,pointerEvents:"none"},[`${e}-checked`]:{marginInlineStart:`calc(-100% + ${i.switchPinSize+i.switchPadding*2}px - ${i.switchInnerMarginMax*2}px)`,marginInlineEnd:`calc(100% - ${i.switchPinSize+i.switchPadding*2}px + ${i.switchInnerMarginMax*2}px)`},[`${e}-unchecked`]:{marginTop:-i.switchHeight,marginInlineStart:0,marginInlineEnd:0}},[`&${n}-checked ${e}`]:{paddingInlineStart:i.switchInnerMarginMin,paddingInlineEnd:i.switchInnerMarginMax,[`${e}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${e}-unchecked`]:{marginInlineStart:`calc(100% - ${i.switchPinSize+i.switchPadding*2}px + ${i.switchInnerMarginMax*2}px)`,marginInlineEnd:`calc(-100% + ${i.switchPinSize+i.switchPadding*2}px - ${i.switchInnerMarginMax*2}px)`}},[`&:not(${n}-disabled):active`]:{[`&:not(${n}-checked) ${e}`]:{[`${e}-unchecked`]:{marginInlineStart:i.switchPadding*2,marginInlineEnd:-i.switchPadding*2}},[`&${n}-checked ${e}`]:{[`${e}-checked`]:{marginInlineStart:-i.switchPadding*2,marginInlineEnd:i.switchPadding*2}}}}}},ei=i=>{const{componentCls:n}=i;return{[n]:w(w(w(w({},V(i)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:i.switchMinWidth,height:i.switchHeight,lineHeight:`${i.switchHeight}px`,verticalAlign:"middle",background:i.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:`all ${i.motionDurationMid}`,userSelect:"none",[`&:hover:not(${n}-disabled)`]:{background:i.colorTextTertiary}}),_(i)),{[`&${n}-checked`]:{background:i.switchColor,[`&:hover:not(${n}-disabled)`]:{background:i.colorPrimaryHover}},[`&${n}-loading, &${n}-disabled`]:{cursor:"not-allowed",opacity:i.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},[`&${n}-rtl`]:{direction:"rtl"}})}},ai=F("Switch",i=>{const n=i.fontSize*i.lineHeight,e=i.controlHeight/2,t=2,h=n-t*2,d=e-t*2,s=L(i,{switchMinWidth:h*2+t*4,switchHeight:n,switchDuration:i.motionDurationMid,switchColor:i.colorPrimary,switchDisabledOpacity:i.opacityLoading,switchInnerMarginMin:h/2,switchInnerMarginMax:h+t+t*2,switchPadding:t,switchPinSize:h,switchBg:i.colorBgContainer,switchMinWidthSM:d*2+t*2,switchHeightSM:e,switchInnerMarginMinSM:d/2,switchInnerMarginMaxSM:d+t+t*2,switchPinSizeSM:d,switchHandleShadow:`0 2px 4px 0 ${new U("#00230b").setAlpha(.2).toRgbString()}`,switchLoadingIconSize:i.fontSizeIcon*.75,switchLoadingIconColor:`rgba(0, 0, 0, ${i.opacityLoading})`,switchHandleActiveInset:"-30%"});return[ei(s),ni(s),ii(s),k(s),Z(s)]}),ci=O("small","default"),ti=()=>({id:String,prefixCls:String,size:c.oneOf(ci),disabled:{type:Boolean,default:void 0},checkedChildren:c.any,unCheckedChildren:c.any,tabindex:c.oneOfType([c.string,c.number]),autofocus:{type:Boolean,default:void 0},loading:{type:Boolean,default:void 0},checked:c.oneOfType([c.string,c.number,c.looseBool]),checkedValue:c.oneOfType([c.string,c.number,c.looseBool]).def(!0),unCheckedValue:c.oneOfType([c.string,c.number,c.looseBool]).def(!1),onChange:{type:Function},onClick:{type:Function},onKeydown:{type:Function},onMouseup:{type:Function},"onUpdate:checked":{type:Function},onBlur:Function,onFocus:Function}),li=G({compatConfig:{MODE:3},name:"ASwitch",__ANT_SWITCH:!0,inheritAttrs:!1,props:ti(),slots:Object,setup(i,n){let{attrs:e,slots:t,expose:h,emit:d}=n;const s=N(),f=A(),g=M(()=>{var a;return(a=i.disabled)!==null&&a!==void 0?a:f.value});J(()=>{});const S=x(i.checked!==void 0?i.checked:e.defaultChecked),m=M(()=>S.value===i.checkedValue);Q(()=>i.checked,()=>{S.value=i.checked});const{prefixCls:l,direction:C,size:y}=X("switch",i),[P,z]=ai(l),u=x(),p=()=>{var a;(a=u.value)===null||a===void 0||a.focus()};h({focus:p,blur:()=>{var a;(a=u.value)===null||a===void 0||a.blur()}}),Y(()=>{q(()=>{i.autofocus&&!g.value&&u.value.focus()})});const $=(a,r)=>{g.value||(d("update:checked",a),d("change",a,r),s.onFieldChange())},H=a=>{d("blur",a)},E=a=>{p();const r=m.value?i.unCheckedValue:i.checkedValue;$(r,a),d("click",r,a)},T=a=>{a.keyCode===b.LEFT?$(i.unCheckedValue,a):a.keyCode===b.RIGHT&&$(i.checkedValue,a),d("keydown",a)},D=a=>{var r;(r=u.value)===null||r===void 0||r.blur(),d("mouseup",a)},B=M(()=>({[`${l.value}-small`]:y.value==="small",[`${l.value}-loading`]:i.loading,[`${l.value}-checked`]:m.value,[`${l.value}-disabled`]:g.value,[l.value]:!0,[`${l.value}-rtl`]:C.value==="rtl",[z.value]:!0}));return()=>{var a;return P(o(R,null,{default:()=>[o("button",I(I(I({},j(i,["prefixCls","checkedChildren","unCheckedChildren","checked","autofocus","checkedValue","unCheckedValue","id","onChange","onUpdate:checked"])),e),{},{id:(a=i.id)!==null&&a!==void 0?a:s.id.value,onKeydown:T,onClick:E,onBlur:H,onMouseup:D,type:"button",role:"switch","aria-checked":S.value,disabled:g.value||i.loading,class:[e.class,B.value],ref:u}),[o("div",{class:`${l.value}-handle`},[i.loading?o(K,{class:`${l.value}-loading-icon`},null):null]),o("span",{class:`${l.value}-inner`},[o("span",{class:`${l.value}-inner-checked`},[v(t,i,"checkedChildren")]),o("span",{class:`${l.value}-inner-unchecked`},[v(t,i,"unCheckedChildren")])])])]}))}}}),hi=W(li);export{ci as SwitchSizes,hi as default,ti as switchProps};
