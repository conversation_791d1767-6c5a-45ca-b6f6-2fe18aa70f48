import{P as O,_ as l,G as lt,H as R,j as M,k as it,m as Z,aK as st,r as pt,F as ft,a as ct,n as ut,i as dt,aL as mt,S as gt,aM as vt,x as W,l as bt}from"./bootstrap-CMNRQ0xm.js";import{T as wt}from"./Trigger-UKZ_Pgvu.js";import{a4 as X,x as T,a5 as H,ao as ht,J as k,P as U,Y as yt}from"../jse/index-index-DjeMElj0.js";import{c as G}from"./vnode-BlmdA_v_.js";import{i as Ct,r as Ot,g as _t}from"./colors-aYeeJJ5E.js";const y={adjustX:1,adjustY:1},C=[0,0],Q={left:{points:["cr","cl"],overflow:y,offset:[-4,0],targetOffset:C},right:{points:["cl","cr"],overflow:y,offset:[4,0],targetOffset:C},top:{points:["bc","tc"],overflow:y,offset:[0,-4],targetOffset:C},bottom:{points:["tc","bc"],overflow:y,offset:[0,4],targetOffset:C},topLeft:{points:["bl","tl"],overflow:y,offset:[0,-4],targetOffset:C},leftTop:{points:["tr","tl"],overflow:y,offset:[-4,0],targetOffset:C},topRight:{points:["br","tr"],overflow:y,offset:[0,-4],targetOffset:C},rightTop:{points:["tl","tr"],overflow:y,offset:[4,0],targetOffset:C},bottomRight:{points:["tr","br"],overflow:y,offset:[0,4],targetOffset:C},rightBottom:{points:["bl","br"],overflow:y,offset:[4,0],targetOffset:C},bottomLeft:{points:["tl","bl"],overflow:y,offset:[0,4],targetOffset:C},leftBottom:{points:["br","bl"],overflow:y,offset:[-4,0],targetOffset:C}},Pt={prefixCls:String,id:String,overlayInnerStyle:O.any},St=X({compatConfig:{MODE:3},name:"TooltipContent",props:Pt,setup(t,o){let{slots:e}=o;return()=>{var r;return T("div",{class:`${t.prefixCls}-inner`,id:t.id,role:"tooltip",style:t.overlayInnerStyle},[(r=e.overlay)===null||r===void 0?void 0:r.call(e)])}}});var Tt=function(t,o){var e={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&o.indexOf(r)<0&&(e[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(t);a<r.length;a++)o.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(t,r[a])&&(e[r[a]]=t[r[a]]);return e};function q(){}const At=X({compatConfig:{MODE:3},name:"Tooltip",inheritAttrs:!1,props:{trigger:O.any.def(["hover"]),defaultVisible:{type:Boolean,default:void 0},visible:{type:Boolean,default:void 0},placement:O.string.def("right"),transitionName:String,animation:O.any,afterVisibleChange:O.func.def(()=>{}),overlayStyle:{type:Object,default:void 0},overlayClassName:String,prefixCls:O.string.def("rc-tooltip"),mouseEnterDelay:O.number.def(.1),mouseLeaveDelay:O.number.def(.1),getPopupContainer:Function,destroyTooltipOnHide:{type:Boolean,default:!1},align:O.object.def(()=>({})),arrowContent:O.any.def(null),tipId:String,builtinPlacements:O.object,overlayInnerStyle:{type:Object,default:void 0},popupVisible:{type:Boolean,default:void 0},onVisibleChange:Function,onPopupAlign:Function,arrow:{type:Boolean,default:!0}},setup(t,o){let{slots:e,attrs:r,expose:a}=o;const s=H(),p=()=>{const{prefixCls:f,tipId:u,overlayInnerStyle:b}=t;return[t.arrow?T("div",{class:`${f}-arrow`,key:"arrow"},[lt(e,t,"arrowContent")]):null,T(St,{key:"content",prefixCls:f,id:u,overlayInnerStyle:b},{overlay:e.overlay})]};a({getPopupDomNode:()=>s.value.getPopupDomNode(),triggerDOM:s,forcePopupAlign:()=>{var f;return(f=s.value)===null||f===void 0?void 0:f.forcePopupAlign()}});const w=H(!1),_=H(!1);return ht(()=>{const{destroyTooltipOnHide:f}=t;if(typeof f=="boolean")w.value=f;else if(f&&typeof f=="object"){const{keepParent:u}=f;w.value=u===!0,_.value=u===!1}}),()=>{const{overlayClassName:f,trigger:u,mouseEnterDelay:b,mouseLeaveDelay:P,overlayStyle:g,prefixCls:D,afterVisibleChange:z,transitionName:B,animation:A,placement:I,align:V,destroyTooltipOnHide:F,defaultVisible:x}=t,L=Tt(t,["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","afterVisibleChange","transitionName","animation","placement","align","destroyTooltipOnHide","defaultVisible"]),N=l({},L);t.visible!==void 0&&(N.popupVisible=t.visible);const E=l(l(l({popupClassName:f,prefixCls:D,action:u,builtinPlacements:Q,popupPlacement:I,popupAlign:V,afterPopupVisibleChange:z,popupTransitionName:B,popupAnimation:A,defaultPopupVisible:x,destroyPopupOnHide:w.value,autoDestroy:_.value,mouseLeaveDelay:P,popupStyle:g,mouseEnterDelay:b},N),r),{onPopupVisibleChange:t.onVisibleChange||q,onPopupAlign:t.onPopupAlign||q,ref:s,arrow:!!t.arrow,popup:p()});return T(wt,E,{default:e.default})}}}),$t=()=>({trigger:[String,Array],open:{type:Boolean,default:void 0},visible:{type:Boolean,default:void 0},placement:String,color:String,transitionName:String,overlayStyle:R(),overlayInnerStyle:R(),overlayClassName:String,openClassName:String,prefixCls:String,mouseEnterDelay:Number,mouseLeaveDelay:Number,getPopupContainer:Function,arrowPointAtCenter:{type:Boolean,default:void 0},arrow:{type:[Boolean,Object],default:!0},autoAdjustOverflow:{type:[Boolean,Object],default:void 0},destroyTooltipOnHide:{type:Boolean,default:void 0},align:R(),builtinPlacements:R(),children:Array,onVisibleChange:Function,"onUpdate:visible":Function,onOpenChange:Function,"onUpdate:open":Function}),xt={adjustX:1,adjustY:1},J={adjustX:0,adjustY:0},kt=[0,0];function K(t){return typeof t=="boolean"?t?xt:J:l(l({},J),t)}function Bt(t){const{arrowWidth:o=4,horizontalArrowShift:e=16,verticalArrowShift:r=8,autoAdjustOverflow:a,arrowPointAtCenter:s}=t,p={left:{points:["cr","cl"],offset:[-4,0]},right:{points:["cl","cr"],offset:[4,0]},top:{points:["bc","tc"],offset:[0,-4]},bottom:{points:["tc","bc"],offset:[0,4]},topLeft:{points:["bl","tc"],offset:[-(e+o),-4]},leftTop:{points:["tr","cl"],offset:[-4,-(r+o)]},topRight:{points:["br","tc"],offset:[e+o,-4]},rightTop:{points:["tl","cr"],offset:[4,-(r+o)]},bottomRight:{points:["tr","bc"],offset:[e+o,4]},rightBottom:{points:["bl","cr"],offset:[4,r+o]},bottomLeft:{points:["tl","bc"],offset:[-(e+o),4]},leftBottom:{points:["br","cl"],offset:[-4,r+o]}};return Object.keys(p).forEach(c=>{p[c]=s?l(l({},p[c]),{overflow:K(a),targetOffset:kt}):l(l({},Q[c]),{overflow:K(a)}),p[c].ignoreShake=!0}),p}function Nt(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];for(let o=0,e=t.length;o<e;o++)if(t[o]!==void 0)return t[o]}function Rt(t,o){const e=Ct(o),r=M({[`${t}-${o}`]:o&&e}),a={},s={};return o&&!e&&(a.background=o,s["--antd-arrow-background-color"]=o),{className:r,overlayStyle:a,arrowStyle:s}}function j(t){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return t.map(e=>`${o}${e}`).join(",")}const tt=8;function jt(t){const o=tt,{sizePopupArrow:e,contentRadius:r,borderRadiusOuter:a}=t,s=e/2-Math.ceil(a*(Math.sqrt(2)-1)),p=(r>12?r+2:12)-s,c=o-s;return{dropdownArrowOffset:p,dropdownArrowOffsetVertical:c}}function Dt(t,o){const{componentCls:e,sizePopupArrow:r,marginXXS:a,borderRadiusXS:s,borderRadiusOuter:p,boxShadowPopoverArrow:c}=t,{colorBg:w,showArrowCls:_,contentRadius:f=t.borderRadiusLG}=o,{dropdownArrowOffsetVertical:u,dropdownArrowOffset:b}=jt({sizePopupArrow:r,contentRadius:f,borderRadiusOuter:p}),P=r/2+a;return{[e]:{[`${e}-arrow`]:[l(l({position:"absolute",zIndex:1,display:"block"},Ot(r,s,p,w,c)),{"&:before":{background:w}})],[[`&-placement-top ${e}-arrow`,`&-placement-topLeft ${e}-arrow`,`&-placement-topRight ${e}-arrow`].join(",")]:{bottom:0,transform:"translateY(100%) rotate(180deg)"},[`&-placement-top ${e}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},[`&-placement-topLeft ${e}-arrow`]:{left:{_skip_check_:!0,value:b}},[`&-placement-topRight ${e}-arrow`]:{right:{_skip_check_:!0,value:b}},[[`&-placement-bottom ${e}-arrow`,`&-placement-bottomLeft ${e}-arrow`,`&-placement-bottomRight ${e}-arrow`].join(",")]:{top:0,transform:"translateY(-100%)"},[`&-placement-bottom ${e}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(-100%)"},[`&-placement-bottomLeft ${e}-arrow`]:{left:{_skip_check_:!0,value:b}},[`&-placement-bottomRight ${e}-arrow`]:{right:{_skip_check_:!0,value:b}},[[`&-placement-left ${e}-arrow`,`&-placement-leftTop ${e}-arrow`,`&-placement-leftBottom ${e}-arrow`].join(",")]:{right:{_skip_check_:!0,value:0},transform:"translateX(100%) rotate(90deg)"},[`&-placement-left ${e}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(100%) rotate(90deg)"},[`&-placement-leftTop ${e}-arrow`]:{top:u},[`&-placement-leftBottom ${e}-arrow`]:{bottom:u},[[`&-placement-right ${e}-arrow`,`&-placement-rightTop ${e}-arrow`,`&-placement-rightBottom ${e}-arrow`].join(",")]:{left:{_skip_check_:!0,value:0},transform:"translateX(-100%) rotate(-90deg)"},[`&-placement-right ${e}-arrow`]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(-100%) rotate(-90deg)"},[`&-placement-rightTop ${e}-arrow`]:{top:u},[`&-placement-rightBottom ${e}-arrow`]:{bottom:u},[j(["&-placement-topLeft","&-placement-top","&-placement-topRight"].map(g=>g+=":not(&-arrow-hidden)"),_)]:{paddingBottom:P},[j(["&-placement-bottomLeft","&-placement-bottom","&-placement-bottomRight"].map(g=>g+=":not(&-arrow-hidden)"),_)]:{paddingTop:P},[j(["&-placement-leftTop","&-placement-left","&-placement-leftBottom"].map(g=>g+=":not(&-arrow-hidden)"),_)]:{paddingRight:{_skip_check_:!0,value:P}},[j(["&-placement-rightTop","&-placement-right","&-placement-rightBottom"].map(g=>g+=":not(&-arrow-hidden)"),_)]:{paddingLeft:{_skip_check_:!0,value:P}}}}}const It=t=>{const{componentCls:o,tooltipMaxWidth:e,tooltipColor:r,tooltipBg:a,tooltipBorderRadius:s,zIndexPopup:p,controlHeight:c,boxShadowSecondary:w,paddingSM:_,paddingXS:f,tooltipRadiusOuter:u}=t;return[{[o]:l(l(l(l({},pt(t)),{position:"absolute",zIndex:p,display:"block","&":[{width:"max-content"},{width:"intrinsic"}],maxWidth:e,visibility:"visible","&-hidden":{display:"none"},"--antd-arrow-background-color":a,[`${o}-inner`]:{minWidth:c,minHeight:c,padding:`${_/2}px ${f}px`,color:r,textAlign:"start",textDecoration:"none",wordWrap:"break-word",backgroundColor:a,borderRadius:s,boxShadow:w},[["&-placement-left","&-placement-leftTop","&-placement-leftBottom","&-placement-right","&-placement-rightTop","&-placement-rightBottom"].join(",")]:{[`${o}-inner`]:{borderRadius:Math.min(s,tt)}},[`${o}-content`]:{position:"relative"}}),_t(t,(b,P)=>{let{darkColor:g}=P;return{[`&${o}-${b}`]:{[`${o}-inner`]:{backgroundColor:g},[`${o}-arrow`]:{"--antd-arrow-background-color":g}}}})),{"&-rtl":{direction:"rtl"}})},Dt(Z(t,{borderRadiusOuter:u}),{colorBg:"var(--antd-arrow-background-color)",showArrowCls:"",contentRadius:s}),{[`${o}-pure`]:{position:"relative",maxWidth:"none"}}]},Vt=(t,o)=>it("Tooltip",r=>{if((o==null?void 0:o.value)===!1)return[];const{borderRadius:a,colorTextLightSolid:s,colorBgDefault:p,borderRadiusOuter:c}=r,w=Z(r,{tooltipMaxWidth:250,tooltipColor:s,tooltipBorderRadius:a,tooltipBg:p,tooltipRadiusOuter:c>4?4:c});return[It(w),st(r,"zoom-big-fast")]},r=>{let{zIndexPopupBase:a,colorBgSpotlight:s}=r;return{zIndexPopup:a+70,colorBgDefault:s}})(t),Lt=(t,o)=>{const e={},r=l({},t);return o.forEach(a=>{t&&a in t&&(e[a]=t[a],delete r[a])}),{picked:e,omitted:r}},Et=()=>l(l({},$t()),{title:O.any}),Ht=X({compatConfig:{MODE:3},name:"ATooltip",inheritAttrs:!1,props:ft(Et(),{trigger:"hover",align:{},placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1,arrowPointAtCenter:!1,autoAdjustOverflow:!0}),slots:Object,setup(t,o){let{slots:e,emit:r,attrs:a,expose:s}=o;const{prefixCls:p,getPopupContainer:c,direction:w,rootPrefixCls:_}=ct("tooltip",t),f=k(()=>{var n;return(n=t.open)!==null&&n!==void 0?n:t.visible}),u=U(Nt([t.open,t.visible])),b=U();let P;yt(f,n=>{W.cancel(P),P=W(()=>{u.value=!!n})});const g=()=>{var n;const i=(n=t.title)!==null&&n!==void 0?n:e.title;return!i&&i!==0},D=n=>{const i=g();f.value===void 0&&(u.value=i?!1:n),i||(r("update:visible",n),r("visibleChange",n),r("update:open",n),r("openChange",n))};s({getPopupDomNode:()=>b.value.getPopupDomNode(),open:u,forcePopupAlign:()=>{var n;return(n=b.value)===null||n===void 0?void 0:n.forcePopupAlign()}});const B=k(()=>{var n;const{builtinPlacements:i,autoAdjustOverflow:S,arrow:m,arrowPointAtCenter:h}=t;let d=h;return typeof m=="object"&&(d=(n=m.pointAtCenter)!==null&&n!==void 0?n:h),i||Bt({arrowPointAtCenter:d,autoAdjustOverflow:S})}),A=n=>n||n==="",I=n=>{const i=n.type;if(typeof i=="object"&&n.props&&((i.__ANT_BUTTON===!0||i==="button")&&A(n.props.disabled)||i.__ANT_SWITCH===!0&&(A(n.props.disabled)||A(n.props.loading))||i.__ANT_RADIO===!0&&A(n.props.disabled))){const{picked:S,omitted:m}=Lt(vt(n),["position","left","right","top","bottom","float","display","zIndex"]),h=l(l({display:"inline-block"},S),{cursor:"not-allowed",lineHeight:1,width:n.props&&n.props.block?"100%":void 0}),d=l(l({},m),{pointerEvents:"none"}),v=G(n,{style:d},!0);return T("span",{style:h,class:`${p.value}-disabled-compatible-wrapper`},[v])}return n},V=()=>{var n,i;return(n=t.title)!==null&&n!==void 0?n:(i=e.title)===null||i===void 0?void 0:i.call(e)},F=(n,i)=>{const S=B.value,m=Object.keys(S).find(h=>{var d,v;return S[h].points[0]===((d=i.points)===null||d===void 0?void 0:d[0])&&S[h].points[1]===((v=i.points)===null||v===void 0?void 0:v[1])});if(m){const h=n.getBoundingClientRect(),d={top:"50%",left:"50%"};m.indexOf("top")>=0||m.indexOf("Bottom")>=0?d.top=`${h.height-i.offset[1]}px`:(m.indexOf("Top")>=0||m.indexOf("bottom")>=0)&&(d.top=`${-i.offset[1]}px`),m.indexOf("left")>=0||m.indexOf("Right")>=0?d.left=`${h.width-i.offset[0]}px`:(m.indexOf("right")>=0||m.indexOf("Left")>=0)&&(d.left=`${-i.offset[0]}px`),n.style.transformOrigin=`${d.left} ${d.top}`}},x=k(()=>Rt(p.value,t.color)),L=k(()=>a["data-popover-inject"]),[N,E]=Vt(p,k(()=>!L.value));return()=>{var n,i;const{openClassName:S,overlayClassName:m,overlayStyle:h,overlayInnerStyle:d}=t;let v=(i=ut((n=e.default)===null||n===void 0?void 0:n.call(e)))!==null&&i!==void 0?i:null;v=v.length===1?v[0]:v;let Y=u.value;if(f.value===void 0&&g()&&(Y=!1),!v)return null;const $=I(dt(v)&&!mt(v)?v:T("span",null,[v])),et=M({[S||`${p.value}-open`]:!0,[$.props&&$.props.class]:$.props&&$.props.class}),ot=M(m,{[`${p.value}-rtl`]:w.value==="rtl"},x.value.className,E.value),nt=l(l({},x.value.overlayStyle),d),rt=x.value.arrowStyle,at=l(l(l({},a),t),{prefixCls:p.value,arrow:!!t.arrow,getPopupContainer:c==null?void 0:c.value,builtinPlacements:B.value,visible:Y,ref:b,overlayClassName:ot,overlayStyle:l(l({},rt),h),overlayInnerStyle:nt,onVisibleChange:D,onPopupAlign:F,transitionName:gt(_.value,"zoom-big-fast",t.transitionName)});return N(T(At,at,{default:()=>[u.value?G($,{class:et}):$],arrowContent:()=>T("span",{class:`${p.value}-arrow-content`},null),overlay:V}))}}}),Wt=bt(Ht);export{Wt as T};
