import{aS as e}from"./bootstrap-CMNRQ0xm.js";import{A as r}from"./authentication-D7VF4q3A.js";import{a4 as s,J as t,a1 as o,aa as c,ab as i,a7 as a}from"../jse/index-index-DjeMElj0.js";import"./theme-toggle.vue_vue_type_script_setup_true_lang-D_vzbc9t.js";const h=s({__name:"auth",setup(m){const p=t(()=>o.app.name),n=t(()=>o.logo.source);return(u,l)=>(i(),c(a(r),{"app-name":p.value,logo:n.value,"page-description":a(e)("authentication.pageDesc"),"page-title":a(e)("authentication.pageTitle")},null,8,["app-name","logo","page-description","page-title"]))}});export{h as default};
