
> @vben-core/shared@5.5.7 build /Volumes/Data/Code/Project/taiwan-student-apply/taiwan-student-apply-web-vben-gemini/packages/@core/base/shared
> pnpm unbuild

ℹ Building shared
ℹ Cleaning dist directory: ./dist
✔ Build succeeded for shared
  dist/store.mjs (total size: 37 B, chunk size: 37 B, exports: *@tanstack/vue-store)

  dist/constants/index.mjs (total size: 1.1 kB, chunk size: 1.1 kB, exports: CSS_VARIABLE_LAYOUT_CONTENT_HEIGHT, CSS_VARIABLE_LAYOUT_CONTENT_WIDTH, CSS_VARIABLE_LAYOUT_FOOTER_HEIGHT, CSS_VARIABLE_LAYOUT_HEADER_HEIGHT, DEFAULT_NAMESPACE, ELEMENT_ID_MAIN_CONTENT, VBEN_ANT_PREVIEW_URL, VBEN_DOC_URL, VBEN_ELE_PREVIEW_URL, VBEN_GITHUB_URL, VBEN_LOGO_URL, VBEN_NAIVE_PREVIEW_URL, VBEN_PREVIEW_URL)

  dist/utils/index.mjs (total size: 14.6 kB, chunk size: 14.6 kB, exports: StateHandler, arraysEqual, bindMethods, capitalizeFirstLetter, cloneDeep, cn, createMerge, diff, downloadFileFromBase64, downloadFileFromBlob, downloadFileFromBlobPart, downloadFileFromImageUrl, downloadFileFromUrl, filterTree, formatDate, formatDateTime, get, getElementVisibleRect, getFirstNonNullOrUndefined, getNestedValue, getScrollbarWidth, isBoolean, isDate, isDayjsObject, isEmpty, isEqual, isFunction, isHttpUrl, isMacOs, isNumber, isObject, isString, isUndefined, isWindow, isWindowsOs, kebabToCamelCase, mapTree, merge, mergeWithArrayOverride, needsScrollbar, openRouteInNewWindow, openWindow, set, startProgress, stopProgress, to, toCamelCase, toLowerCaseFirstLetter, traverseTreeValues, triggerDownload, triggerWindowResize, uniqueByField, updateCSSVariables, urlToBase64)

  dist/color/index.mjs (total size: 1.9 kB, chunk size: 1.9 kB, exports: TinyColor, convertToHsl, convertToHslCssVar, convertToRgb, generatorColorVariables, isDarkColor, isLightColor, isValidColor)

  dist/cache/index.mjs (total size: 2.47 kB, chunk size: 2.47 kB, exports: StorageManager)

  dist/global-state.mjs (total size: 473 B, chunk size: 473 B, exports: globalShareState)

Σ Total dist size (byte size): 61.8 kB

