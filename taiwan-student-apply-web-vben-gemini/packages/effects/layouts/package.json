{"name": "@vben/layouts", "version": "5.5.7", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/effects/layouts"}, "license": "MIT", "type": "module", "sideEffects": ["**/*.css"], "exports": {".": {"default": "./src/index.ts"}}, "dependencies": {"@vben-core/composables": "workspace:*", "@vben-core/form-ui": "workspace:*", "@vben-core/layout-ui": "workspace:*", "@vben-core/menu-ui": "workspace:*", "@vben-core/popup-ui": "workspace:*", "@vben-core/shadcn-ui": "workspace:*", "@vben-core/shared": "workspace:*", "@vben-core/tabs-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/locales": "workspace:*", "@vben/preferences": "workspace:*", "@vben/stores": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}}