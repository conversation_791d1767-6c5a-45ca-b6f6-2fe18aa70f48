{"name": "@poppinss/exception", "description": "Utility to create custom exceptions", "version": "1.2.1", "engines": {"node": ">=18"}, "type": "module", "files": ["build", "!build/bin", "!build/tests"], "main": "build/index.js", "exports": {".": "./build/index.js"}, "scripts": {"pretest": "npm run lint", "test": "c8 npm run quick:test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit", "precompile": "npm run lint", "compile": "tsup-node && tsc --emitDeclarationOnly --declaration", "build": "npm run compile", "version": "npm run build", "prepublishOnly": "npm run build", "release": "release-it", "quick:test": "node --import=ts-node-maintained/register/esm --enable-source-maps bin/test.ts"}, "devDependencies": {"@adonisjs/eslint-config": "^2.0.0", "@adonisjs/prettier-config": "^1.4.2", "@adonisjs/tsconfig": "^1.4.0", "@japa/expect": "^3.0.3", "@japa/expect-type": "^2.0.3", "@japa/runner": "^4.2.0", "@release-it/conventional-changelog": "^10.0.0", "@swc/core": "1.10.7", "@types/node": "^22.13.9", "c8": "^10.1.3", "eslint": "^9.21.0", "prettier": "^3.5.3", "release-it": "^18.1.2", "ts-node-maintained": "^10.9.5", "tsup": "^8.4.0", "typescript": "^5.8.2"}, "homepage": "https://github.com/poppinss/exception#readme", "repository": {"type": "git", "url": "git+https://github.com/poppinss/exception.git"}, "bugs": {"url": "https://github.com/poppinss/exception/issues"}, "keywords": [], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "publishConfig": {"access": "public", "provenance": true}, "tsup": {"entry": ["index.ts"], "outDir": "./build", "clean": true, "format": "esm", "dts": false, "sourcemap": false, "target": "esnext"}, "release-it": {"git": {"requireCleanWorkingDir": true, "requireUpstream": true, "commitMessage": "chore(release): ${version}", "tagAnnotation": "v${version}", "push": true, "tagName": "v${version}"}, "github": {"release": true}, "npm": {"publish": true, "skipChecks": true}, "plugins": {"@release-it/conventional-changelog": {"preset": {"name": "angular"}}}}, "c8": {"reporter": ["text", "html"], "exclude": ["tests/**"]}, "prettier": "@adonisjs/prettier-config"}