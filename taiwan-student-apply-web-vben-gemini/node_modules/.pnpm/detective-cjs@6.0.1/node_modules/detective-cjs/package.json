{"name": "detective-cjs", "version": "6.0.1", "description": "Get the dependencies of a CommonJS module by traversing its AST", "main": "index.js", "files": ["index.js"], "scripts": {"lint": "xo", "fix": "xo --fix", "mocha": "mocha", "test": "npm run lint && npm run mocha", "test:ci": "c8 npm run mocha"}, "repository": {"type": "git", "url": "git+https://github.com/dependents/node-detective-cjs.git"}, "keywords": ["detective", "commonjs", "dependencies", "ast", "static analysis", "cjs", "jsx"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dependents/node-detective-cjs/issues"}, "homepage": "https://github.com/dependents/node-detective-cjs", "engines": {"node": ">=18"}, "dependencies": {"ast-module-types": "^6.0.1", "node-source-walk": "^7.0.1"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.1.0", "xo": "^0.60.0"}, "xo": {"space": true, "ignores": ["test/fixtures/*"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "curly": ["error", "multi-line"], "operator-linebreak": ["error", "after"], "object-curly-spacing": ["error", "always"], "space-before-function-paren": ["error", "never"], "unicorn/no-anonymous-default-export": "off", "unicorn/prefer-module": "off", "unicorn/prefer-node-protocol": "off", "unicorn/prefer-top-level-await": "off", "unicorn/prevent-abbreviations": "off"}}}