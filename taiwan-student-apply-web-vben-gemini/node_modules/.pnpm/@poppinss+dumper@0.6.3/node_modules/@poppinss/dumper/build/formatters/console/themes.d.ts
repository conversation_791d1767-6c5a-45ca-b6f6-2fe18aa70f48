/**
 * Default styles to use for pretty printing to ANSI output
 */
export declare const themes: {
    default: {
        braces: (value: string) => string;
        brackets: (value: string) => string;
        number: (value: string) => string;
        bigInt: (value: string) => string;
        boolean: (value: string) => string;
        string: (value: string) => string;
        null: (value: string) => string;
        undefined: (value: string) => string;
        prototypeLabel: (value: string) => string;
        symbol: (value: string) => string;
        regex: (value: string) => string;
        date: (value: string) => string;
        buffer: (value: string) => string;
        functionLabel: (value: string) => string;
        arrayLabel: (value: string) => string;
        objectLabel: (value: string) => string;
        mapLabel: (value: string) => string;
        setLabel: (value: string) => string;
        objectKey: (value: string) => string;
        objectKeyPrefix: (value: string) => string;
        classLabel: (value: string) => string;
        weakSetLabel: (value: string) => string;
        weakRefLabel: (value: string) => string;
        collapseLabel: (value: string) => string;
        circularLabel: (value: string) => string;
        getterLabel: (value: string) => string;
        weakMapLabel: (value: string) => string;
        observableLabel: (value: string) => string;
        promiseLabel: (value: string) => string;
        generatorLabel: (value: string) => string;
        blobLabel: (value: string) => string;
        unknownLabel: (value: string) => string;
    };
};
