{"name": "unicorn-magic", "version": "0.3.0", "description": "Some useful utilities I often need", "license": "MIT", "repository": "sindresorhus/unicorn-magic", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"node": {"types": "./node.d.ts", "import": "./node.js"}, "default": {"types": "./default.d.ts", "import": "./default.js"}}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsc node.d.ts"}, "files": ["node.js", "node.d.ts", "default.js", "default.d.ts"], "keywords": ["utilities", "util", "extras", "url", "path", "delay", "wait", "settimeout", "sleep", "child_process", "child", "process", "subprocess", "exec", "execfile", "execfilesync"], "devDependencies": {"ava": "^6.1.3", "in-range": "^3.0.0", "time-span": "^5.1.0", "typescript": "^5.5.4", "xo": "^0.59.2"}}