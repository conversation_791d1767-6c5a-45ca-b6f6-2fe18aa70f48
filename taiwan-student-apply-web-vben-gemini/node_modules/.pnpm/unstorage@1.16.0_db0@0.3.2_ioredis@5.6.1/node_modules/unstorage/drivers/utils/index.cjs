"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.createError = createError;
exports.createRequiredError = createRequiredError;
exports.defineDriver = defineDriver;
exports.joinKeys = joinKeys;
exports.normalizeKey = normalizeKey;
function defineDriver(factory) {
  return factory;
}
function normalizeKey(key, sep = ":") {
  if (!key) {
    return "";
  }
  return key.replace(/[:/\\]/g, sep).replace(/^[:/\\]|[:/\\]$/g, "");
}
function joinKeys(...keys) {
  return keys.map(key => normalizeKey(key)).filter(Boolean).join(":");
}
function createError(driver, message, opts) {
  const err = new Error(`[unstorage] [${driver}] ${message}`, opts);
  if (Error.captureStackTrace) {
    Error.captureStackTrace(err, createError);
  }
  return err;
}
function createRequiredError(driver, name) {
  if (Array.isArray(name)) {
    return createError(driver, `Missing some of the required options ${name.map(n => "`" + n + "`").join(", ")}`);
  }
  return createError(driver, `Missing required option \`${name}\`.`);
}