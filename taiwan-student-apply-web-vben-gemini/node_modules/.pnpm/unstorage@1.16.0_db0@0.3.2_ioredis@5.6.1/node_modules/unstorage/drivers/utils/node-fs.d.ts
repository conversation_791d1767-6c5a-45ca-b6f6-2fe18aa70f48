import { Dirent, promises as fsPromises } from "node:fs";
type WriteFileData = Parameters<typeof fsPromises.writeFile>[1];
export declare function writeFile(path: string, data: WriteFileData, encoding?: BufferEncoding): Promise<void>;
export declare function readFile(path: string, encoding?: BufferEncoding): Promise<any>;
export declare function stat(path: string): Promise<any>;
export declare function unlink(path: string): Promise<any>;
export declare function readdir(dir: string): Promise<Dirent[]>;
export declare function ensuredir(dir: string): Promise<void>;
export declare function readdirRecursive(dir: string, ignore?: (p: string) => boolean, maxDepth?: number): Promise<string[]>;
export declare function rmRecursive(dir: string): Promise<void>;
export {};
