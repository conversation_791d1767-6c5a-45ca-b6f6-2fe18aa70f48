{"name": "@dependents/detective-less", "version": "5.0.1", "description": "Find the dependencies of a less file", "main": "index.js", "publishConfig": {"access": "public"}, "files": ["index.js"], "scripts": {"lint": "xo", "fix": "xo --fix", "uvu": "uvu test -i fixtures", "test": "npm run lint && npm run uvu", "test:ci": "c8 npm run uvu"}, "repository": {"type": "git", "url": "git+https://github.com/dependents/node-detective-less.git"}, "keywords": ["detective", "less", "ast", "dependencies"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <micha<PERSON>@whiteshadows.me>"], "license": "MIT", "bugs": {"url": "https://github.com/dependents/node-detective-less/issues"}, "homepage": "https://github.com/dependents/node-detective-less", "engines": {"node": ">=18"}, "dependencies": {"gonzales-pe": "^4.3.0", "node-source-walk": "^7.0.1"}, "devDependencies": {"c8": "^10.1.3", "uvu": "^0.5.6", "xo": "^0.60.0"}, "xo": {"space": true, "ignores": ["test/fixtures/*"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "curly": ["error", "multi-line"], "operator-linebreak": ["error", "after"], "object-curly-spacing": ["error", "always"], "space-before-function-paren": ["error", "never"], "unicorn/prefer-module": "off", "unicorn/prefer-node-protocol": "off", "unicorn/prefer-top-level-await": "off", "unicorn/prevent-abbreviations": "off"}}}