// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`WritableStream > Atom feed 1`] = `
[
  {
    "$event": "processinginstruction",
    "data": [
      "?xml",
      "?xml version="1.0" encoding="utf-8"?",
    ],
    "endIndex": 37,
    "startIndex": 0,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 38,
    "startIndex": 38,
  },
  {
    "$event": "comment",
    "data": [
      " http://en.wikipedia.org/wiki/Atom_%28standard%29 ",
    ],
    "endIndex": 95,
    "startIndex": 39,
  },
  {
    "$event": "commentend",
    "data": [],
    "endIndex": 95,
    "startIndex": 39,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 96,
    "startIndex": 96,
  },
  {
    "$event": "opentagname",
    "data": [
      "feed",
    ],
    "endIndex": 102,
    "startIndex": 97,
  },
  {
    "$event": "attribute",
    "data": [
      "xmlns",
      "http://www.w3.org/2005/Atom",
      """,
    ],
    "endIndex": 138,
    "startIndex": 103,
  },
  {
    "$event": "opentag",
    "data": [
      "feed",
      {
        "xmlns": "http://www.w3.org/2005/Atom",
      },
      false,
    ],
    "endIndex": 138,
    "startIndex": 97,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 140,
    "startIndex": 139,
  },
  {
    "$event": "opentagname",
    "data": [
      "title",
    ],
    "endIndex": 147,
    "startIndex": 141,
  },
  {
    "$event": "opentag",
    "data": [
      "title",
      {},
      false,
    ],
    "endIndex": 147,
    "startIndex": 141,
  },
  {
    "$event": "text",
    "data": [
      "Example Feed",
    ],
    "endIndex": 159,
    "startIndex": 148,
  },
  {
    "$event": "closetag",
    "data": [
      "title",
      false,
    ],
    "endIndex": 167,
    "startIndex": 160,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 169,
    "startIndex": 168,
  },
  {
    "$event": "opentagname",
    "data": [
      "subtitle",
    ],
    "endIndex": 179,
    "startIndex": 170,
  },
  {
    "$event": "opentag",
    "data": [
      "subtitle",
      {},
      false,
    ],
    "endIndex": 179,
    "startIndex": 170,
  },
  {
    "$event": "text",
    "data": [
      "A subtitle.",
    ],
    "endIndex": 190,
    "startIndex": 180,
  },
  {
    "$event": "closetag",
    "data": [
      "subtitle",
      false,
    ],
    "endIndex": 201,
    "startIndex": 191,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 203,
    "startIndex": 202,
  },
  {
    "$event": "opentagname",
    "data": [
      "link",
    ],
    "endIndex": 209,
    "startIndex": 204,
  },
  {
    "$event": "attribute",
    "data": [
      "href",
      "http://example.org/feed/",
      """,
    ],
    "endIndex": 241,
    "startIndex": 210,
  },
  {
    "$event": "attribute",
    "data": [
      "rel",
      "self",
      """,
    ],
    "endIndex": 252,
    "startIndex": 242,
  },
  {
    "$event": "opentag",
    "data": [
      "link",
      {
        "href": "http://example.org/feed/",
        "rel": "self",
      },
      false,
    ],
    "endIndex": 254,
    "startIndex": 204,
  },
  {
    "$event": "closetag",
    "data": [
      "link",
      true,
    ],
    "endIndex": 254,
    "startIndex": 204,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 256,
    "startIndex": 255,
  },
  {
    "$event": "opentagname",
    "data": [
      "link",
    ],
    "endIndex": 262,
    "startIndex": 257,
  },
  {
    "$event": "attribute",
    "data": [
      "href",
      "http://example.org/",
      """,
    ],
    "endIndex": 289,
    "startIndex": 263,
  },
  {
    "$event": "opentag",
    "data": [
      "link",
      {
        "href": "http://example.org/",
      },
      false,
    ],
    "endIndex": 291,
    "startIndex": 257,
  },
  {
    "$event": "closetag",
    "data": [
      "link",
      true,
    ],
    "endIndex": 291,
    "startIndex": 257,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 293,
    "startIndex": 292,
  },
  {
    "$event": "opentagname",
    "data": [
      "id",
    ],
    "endIndex": 297,
    "startIndex": 294,
  },
  {
    "$event": "opentag",
    "data": [
      "id",
      {},
      false,
    ],
    "endIndex": 297,
    "startIndex": 294,
  },
  {
    "$event": "text",
    "data": [
      "urn:uuid:60a76c80-d399-11d9-b91C-0003939e0af6",
    ],
    "endIndex": 342,
    "startIndex": 298,
  },
  {
    "$event": "closetag",
    "data": [
      "id",
      false,
    ],
    "endIndex": 347,
    "startIndex": 343,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 349,
    "startIndex": 348,
  },
  {
    "$event": "opentagname",
    "data": [
      "updated",
    ],
    "endIndex": 358,
    "startIndex": 350,
  },
  {
    "$event": "opentag",
    "data": [
      "updated",
      {},
      false,
    ],
    "endIndex": 358,
    "startIndex": 350,
  },
  {
    "$event": "text",
    "data": [
      "2003-12-13T18:30:02Z",
    ],
    "endIndex": 378,
    "startIndex": 359,
  },
  {
    "$event": "closetag",
    "data": [
      "updated",
      false,
    ],
    "endIndex": 388,
    "startIndex": 379,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 390,
    "startIndex": 389,
  },
  {
    "$event": "opentagname",
    "data": [
      "author",
    ],
    "endIndex": 398,
    "startIndex": 391,
  },
  {
    "$event": "opentag",
    "data": [
      "author",
      {},
      false,
    ],
    "endIndex": 398,
    "startIndex": 391,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 401,
    "startIndex": 399,
  },
  {
    "$event": "opentagname",
    "data": [
      "name",
    ],
    "endIndex": 407,
    "startIndex": 402,
  },
  {
    "$event": "opentag",
    "data": [
      "name",
      {},
      false,
    ],
    "endIndex": 407,
    "startIndex": 402,
  },
  {
    "$event": "text",
    "data": [
      "John Doe",
    ],
    "endIndex": 415,
    "startIndex": 408,
  },
  {
    "$event": "closetag",
    "data": [
      "name",
      false,
    ],
    "endIndex": 422,
    "startIndex": 416,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 425,
    "startIndex": 423,
  },
  {
    "$event": "opentagname",
    "data": [
      "email",
    ],
    "endIndex": 432,
    "startIndex": 426,
  },
  {
    "$event": "opentag",
    "data": [
      "email",
      {},
      false,
    ],
    "endIndex": 432,
    "startIndex": 426,
  },
  {
    "$event": "text",
    "data": [
      "<EMAIL>",
    ],
    "endIndex": 451,
    "startIndex": 433,
  },
  {
    "$event": "closetag",
    "data": [
      "email",
      false,
    ],
    "endIndex": 459,
    "startIndex": 452,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 461,
    "startIndex": 460,
  },
  {
    "$event": "closetag",
    "data": [
      "author",
      false,
    ],
    "endIndex": 470,
    "startIndex": 462,
  },
  {
    "$event": "text",
    "data": [
      "

	",
    ],
    "endIndex": 473,
    "startIndex": 471,
  },
  {
    "$event": "opentagname",
    "data": [
      "entry",
    ],
    "endIndex": 480,
    "startIndex": 474,
  },
  {
    "$event": "opentag",
    "data": [
      "entry",
      {},
      false,
    ],
    "endIndex": 480,
    "startIndex": 474,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 483,
    "startIndex": 481,
  },
  {
    "$event": "opentagname",
    "data": [
      "title",
    ],
    "endIndex": 490,
    "startIndex": 484,
  },
  {
    "$event": "opentag",
    "data": [
      "title",
      {},
      false,
    ],
    "endIndex": 490,
    "startIndex": 484,
  },
  {
    "$event": "text",
    "data": [
      "Atom-Powered Robots Run Amok",
    ],
    "endIndex": 518,
    "startIndex": 491,
  },
  {
    "$event": "closetag",
    "data": [
      "title",
      false,
    ],
    "endIndex": 526,
    "startIndex": 519,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 529,
    "startIndex": 527,
  },
  {
    "$event": "opentagname",
    "data": [
      "link",
    ],
    "endIndex": 535,
    "startIndex": 530,
  },
  {
    "$event": "attribute",
    "data": [
      "href",
      "http://example.org/2003/12/13/atom03",
      """,
    ],
    "endIndex": 579,
    "startIndex": 536,
  },
  {
    "$event": "opentag",
    "data": [
      "link",
      {
        "href": "http://example.org/2003/12/13/atom03",
      },
      false,
    ],
    "endIndex": 581,
    "startIndex": 530,
  },
  {
    "$event": "closetag",
    "data": [
      "link",
      true,
    ],
    "endIndex": 581,
    "startIndex": 530,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 584,
    "startIndex": 582,
  },
  {
    "$event": "opentagname",
    "data": [
      "link",
    ],
    "endIndex": 590,
    "startIndex": 585,
  },
  {
    "$event": "attribute",
    "data": [
      "rel",
      "alternate",
      """,
    ],
    "endIndex": 606,
    "startIndex": 591,
  },
  {
    "$event": "attribute",
    "data": [
      "type",
      "text/html",
      """,
    ],
    "endIndex": 623,
    "startIndex": 607,
  },
  {
    "$event": "attribute",
    "data": [
      "href",
      "http://example.org/2003/12/13/atom03.html",
      """,
    ],
    "endIndex": 672,
    "startIndex": 624,
  },
  {
    "$event": "opentag",
    "data": [
      "link",
      {
        "href": "http://example.org/2003/12/13/atom03.html",
        "rel": "alternate",
        "type": "text/html",
      },
      false,
    ],
    "endIndex": 673,
    "startIndex": 585,
  },
  {
    "$event": "closetag",
    "data": [
      "link",
      true,
    ],
    "endIndex": 673,
    "startIndex": 585,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 676,
    "startIndex": 674,
  },
  {
    "$event": "opentagname",
    "data": [
      "link",
    ],
    "endIndex": 682,
    "startIndex": 677,
  },
  {
    "$event": "attribute",
    "data": [
      "rel",
      "edit",
      """,
    ],
    "endIndex": 693,
    "startIndex": 683,
  },
  {
    "$event": "attribute",
    "data": [
      "href",
      "http://example.org/2003/12/13/atom03/edit",
      """,
    ],
    "endIndex": 742,
    "startIndex": 694,
  },
  {
    "$event": "opentag",
    "data": [
      "link",
      {
        "href": "http://example.org/2003/12/13/atom03/edit",
        "rel": "edit",
      },
      false,
    ],
    "endIndex": 743,
    "startIndex": 677,
  },
  {
    "$event": "closetag",
    "data": [
      "link",
      true,
    ],
    "endIndex": 743,
    "startIndex": 677,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 746,
    "startIndex": 744,
  },
  {
    "$event": "opentagname",
    "data": [
      "id",
    ],
    "endIndex": 750,
    "startIndex": 747,
  },
  {
    "$event": "opentag",
    "data": [
      "id",
      {},
      false,
    ],
    "endIndex": 750,
    "startIndex": 747,
  },
  {
    "$event": "text",
    "data": [
      "urn:uuid:1225c695-cfb8-4ebb-aaaa-80da344efa6a",
    ],
    "endIndex": 795,
    "startIndex": 751,
  },
  {
    "$event": "closetag",
    "data": [
      "id",
      false,
    ],
    "endIndex": 800,
    "startIndex": 796,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 803,
    "startIndex": 801,
  },
  {
    "$event": "opentagname",
    "data": [
      "updated",
    ],
    "endIndex": 812,
    "startIndex": 804,
  },
  {
    "$event": "opentag",
    "data": [
      "updated",
      {},
      false,
    ],
    "endIndex": 812,
    "startIndex": 804,
  },
  {
    "$event": "text",
    "data": [
      "2003-12-13T18:30:02Z",
    ],
    "endIndex": 832,
    "startIndex": 813,
  },
  {
    "$event": "closetag",
    "data": [
      "updated",
      false,
    ],
    "endIndex": 842,
    "startIndex": 833,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 845,
    "startIndex": 843,
  },
  {
    "$event": "opentagname",
    "data": [
      "content",
    ],
    "endIndex": 854,
    "startIndex": 846,
  },
  {
    "$event": "attribute",
    "data": [
      "type",
      "html",
      """,
    ],
    "endIndex": 866,
    "startIndex": 855,
  },
  {
    "$event": "opentag",
    "data": [
      "content",
      {
        "type": "html",
      },
      false,
    ],
    "endIndex": 866,
    "startIndex": 846,
  },
  {
    "$event": "opentagname",
    "data": [
      "p",
    ],
    "endIndex": 869,
    "startIndex": 867,
  },
  {
    "$event": "opentag",
    "data": [
      "p",
      {},
      false,
    ],
    "endIndex": 869,
    "startIndex": 867,
  },
  {
    "$event": "text",
    "data": [
      "Some content.",
    ],
    "endIndex": 882,
    "startIndex": 870,
  },
  {
    "$event": "closetag",
    "data": [
      "p",
      false,
    ],
    "endIndex": 886,
    "startIndex": 883,
  },
  {
    "$event": "closetag",
    "data": [
      "content",
      false,
    ],
    "endIndex": 896,
    "startIndex": 887,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 898,
    "startIndex": 897,
  },
  {
    "$event": "closetag",
    "data": [
      "entry",
      false,
    ],
    "endIndex": 906,
    "startIndex": 899,
  },
  {
    "$event": "text",
    "data": [
      "

	",
    ],
    "endIndex": 909,
    "startIndex": 907,
  },
  {
    "$event": "opentagname",
    "data": [
      "entry",
    ],
    "endIndex": 916,
    "startIndex": 910,
  },
  {
    "$event": "opentag",
    "data": [
      "entry",
      {},
      false,
    ],
    "endIndex": 917,
    "startIndex": 910,
  },
  {
    "$event": "closetag",
    "data": [
      "entry",
      true,
    ],
    "endIndex": 917,
    "startIndex": 910,
  },
  {
    "$event": "text",
    "data": [
      "

",
    ],
    "endIndex": 919,
    "startIndex": 918,
  },
  {
    "$event": "closetag",
    "data": [
      "feed",
      false,
    ],
    "endIndex": 926,
    "startIndex": 920,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 927,
    "startIndex": 927,
  },
]
`;

exports[`WritableStream > Attributes 1`] = `
[
  {
    "$event": "processinginstruction",
    "data": [
      "!doctype",
      "!doctype html",
    ],
    "endIndex": 14,
    "startIndex": 0,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 15,
    "startIndex": 15,
  },
  {
    "$event": "opentagname",
    "data": [
      "html",
    ],
    "endIndex": 21,
    "startIndex": 16,
  },
  {
    "$event": "opentag",
    "data": [
      "html",
      {},
      false,
    ],
    "endIndex": 21,
    "startIndex": 16,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 22,
    "startIndex": 22,
  },
  {
    "$event": "opentagname",
    "data": [
      "head",
    ],
    "endIndex": 28,
    "startIndex": 23,
  },
  {
    "$event": "opentag",
    "data": [
      "head",
      {},
      false,
    ],
    "endIndex": 28,
    "startIndex": 23,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 30,
    "startIndex": 29,
  },
  {
    "$event": "opentagname",
    "data": [
      "title",
    ],
    "endIndex": 37,
    "startIndex": 31,
  },
  {
    "$event": "opentag",
    "data": [
      "title",
      {},
      false,
    ],
    "endIndex": 37,
    "startIndex": 31,
  },
  {
    "$event": "text",
    "data": [
      "Attributes test",
    ],
    "endIndex": 52,
    "startIndex": 38,
  },
  {
    "$event": "closetag",
    "data": [
      "title",
      false,
    ],
    "endIndex": 60,
    "startIndex": 53,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 61,
    "startIndex": 61,
  },
  {
    "$event": "closetag",
    "data": [
      "head",
      false,
    ],
    "endIndex": 68,
    "startIndex": 62,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 69,
    "startIndex": 69,
  },
  {
    "$event": "opentagname",
    "data": [
      "body",
    ],
    "endIndex": 75,
    "startIndex": 70,
  },
  {
    "$event": "opentag",
    "data": [
      "body",
      {},
      false,
    ],
    "endIndex": 75,
    "startIndex": 70,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 77,
    "startIndex": 76,
  },
  {
    "$event": "comment",
    "data": [
      " Normal attributes ",
    ],
    "endIndex": 103,
    "startIndex": 78,
  },
  {
    "$event": "commentend",
    "data": [],
    "endIndex": 103,
    "startIndex": 78,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 105,
    "startIndex": 104,
  },
  {
    "$event": "opentagname",
    "data": [
      "button",
    ],
    "endIndex": 113,
    "startIndex": 106,
  },
  {
    "$event": "attribute",
    "data": [
      "id",
      "test0",
      """,
    ],
    "endIndex": 124,
    "startIndex": 114,
  },
  {
    "$event": "attribute",
    "data": [
      "class",
      "value0",
      """,
    ],
    "endIndex": 139,
    "startIndex": 125,
  },
  {
    "$event": "attribute",
    "data": [
      "title",
      "value1",
      """,
    ],
    "endIndex": 154,
    "startIndex": 140,
  },
  {
    "$event": "opentag",
    "data": [
      "button",
      {
        "class": "value0",
        "id": "test0",
        "title": "value1",
      },
      false,
    ],
    "endIndex": 154,
    "startIndex": 106,
  },
  {
    "$event": "text",
    "data": [
      "class="value0" title="value1"",
    ],
    "endIndex": 183,
    "startIndex": 155,
  },
  {
    "$event": "closetag",
    "data": [
      "button",
      false,
    ],
    "endIndex": 192,
    "startIndex": 184,
  },
  {
    "$event": "text",
    "data": [
      "

	",
    ],
    "endIndex": 195,
    "startIndex": 193,
  },
  {
    "$event": "comment",
    "data": [
      " Attributes with no quotes or value ",
    ],
    "endIndex": 238,
    "startIndex": 196,
  },
  {
    "$event": "commentend",
    "data": [],
    "endIndex": 238,
    "startIndex": 196,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 240,
    "startIndex": 239,
  },
  {
    "$event": "opentagname",
    "data": [
      "button",
    ],
    "endIndex": 248,
    "startIndex": 241,
  },
  {
    "$event": "attribute",
    "data": [
      "id",
      "test1",
      """,
    ],
    "endIndex": 259,
    "startIndex": 249,
  },
  {
    "$event": "attribute",
    "data": [
      "class",
      "value2",
      null,
    ],
    "endIndex": 272,
    "startIndex": 260,
  },
  {
    "$event": "attribute",
    "data": [
      "disabled",
      "",
    ],
    "endIndex": 281,
    "startIndex": 273,
  },
  {
    "$event": "opentag",
    "data": [
      "button",
      {
        "class": "value2",
        "disabled": "",
        "id": "test1",
      },
      false,
    ],
    "endIndex": 281,
    "startIndex": 241,
  },
  {
    "$event": "text",
    "data": [
      "class=value2 disabled",
    ],
    "endIndex": 302,
    "startIndex": 282,
  },
  {
    "$event": "closetag",
    "data": [
      "button",
      false,
    ],
    "endIndex": 311,
    "startIndex": 303,
  },
  {
    "$event": "text",
    "data": [
      "

	",
    ],
    "endIndex": 314,
    "startIndex": 312,
  },
  {
    "$event": "comment",
    "data": [
      " Attributes with no space between them. No valid, but accepted by the browser ",
    ],
    "endIndex": 399,
    "startIndex": 315,
  },
  {
    "$event": "commentend",
    "data": [],
    "endIndex": 399,
    "startIndex": 315,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 401,
    "startIndex": 400,
  },
  {
    "$event": "opentagname",
    "data": [
      "button",
    ],
    "endIndex": 409,
    "startIndex": 402,
  },
  {
    "$event": "attribute",
    "data": [
      "id",
      "test2",
      """,
    ],
    "endIndex": 420,
    "startIndex": 410,
  },
  {
    "$event": "attribute",
    "data": [
      "class",
      "value4",
      """,
    ],
    "endIndex": 435,
    "startIndex": 421,
  },
  {
    "$event": "attribute",
    "data": [
      "title",
      "value5",
      """,
    ],
    "endIndex": 449,
    "startIndex": 435,
  },
  {
    "$event": "opentag",
    "data": [
      "button",
      {
        "class": "value4",
        "id": "test2",
        "title": "value5",
      },
      false,
    ],
    "endIndex": 449,
    "startIndex": 402,
  },
  {
    "$event": "text",
    "data": [
      "class="value4"title="value5"",
    ],
    "endIndex": 477,
    "startIndex": 450,
  },
  {
    "$event": "closetag",
    "data": [
      "button",
      false,
    ],
    "endIndex": 486,
    "startIndex": 478,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 487,
    "startIndex": 487,
  },
  {
    "$event": "closetag",
    "data": [
      "body",
      false,
    ],
    "endIndex": 494,
    "startIndex": 488,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 495,
    "startIndex": 495,
  },
  {
    "$event": "closetag",
    "data": [
      "html",
      false,
    ],
    "endIndex": 502,
    "startIndex": 496,
  },
]
`;

exports[`WritableStream > Basic html 1`] = `
[
  {
    "$event": "processinginstruction",
    "data": [
      "!doctype",
      "!DOCTYPE html",
    ],
    "endIndex": 14,
    "startIndex": 0,
  },
  {
    "$event": "opentagname",
    "data": [
      "html",
    ],
    "endIndex": 20,
    "startIndex": 15,
  },
  {
    "$event": "opentag",
    "data": [
      "html",
      {},
      false,
    ],
    "endIndex": 20,
    "startIndex": 15,
  },
  {
    "$event": "opentagname",
    "data": [
      "title",
    ],
    "endIndex": 27,
    "startIndex": 21,
  },
  {
    "$event": "opentag",
    "data": [
      "title",
      {},
      false,
    ],
    "endIndex": 27,
    "startIndex": 21,
  },
  {
    "$event": "text",
    "data": [
      "The Title",
    ],
    "endIndex": 36,
    "startIndex": 28,
  },
  {
    "$event": "closetag",
    "data": [
      "title",
      false,
    ],
    "endIndex": 44,
    "startIndex": 37,
  },
  {
    "$event": "opentagname",
    "data": [
      "body",
    ],
    "endIndex": 50,
    "startIndex": 45,
  },
  {
    "$event": "opentag",
    "data": [
      "body",
      {},
      false,
    ],
    "endIndex": 50,
    "startIndex": 45,
  },
  {
    "$event": "text",
    "data": [
      "Hello world",
    ],
    "endIndex": 61,
    "startIndex": 51,
  },
  {
    "$event": "closetag",
    "data": [
      "body",
      false,
    ],
    "endIndex": 68,
    "startIndex": 62,
  },
  {
    "$event": "closetag",
    "data": [
      "html",
      false,
    ],
    "endIndex": 75,
    "startIndex": 69,
  },
]
`;

exports[`WritableStream > RDF feed 1`] = `
[
  {
    "$event": "processinginstruction",
    "data": [
      "?xml",
      "?xml version="1.0" encoding="UTF-8"?",
    ],
    "endIndex": 37,
    "startIndex": 0,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 38,
    "startIndex": 38,
  },
  {
    "$event": "opentagname",
    "data": [
      "rdf:RDF",
    ],
    "endIndex": 47,
    "startIndex": 39,
  },
  {
    "$event": "attribute",
    "data": [
      "xmlns:rdf",
      "http://www.w3.org/1999/02/22-rdf-syntax-ns#",
      """,
    ],
    "endIndex": 103,
    "startIndex": 48,
  },
  {
    "$event": "attribute",
    "data": [
      "xmlns",
      "http://purl.org/rss/1.0/",
      """,
    ],
    "endIndex": 136,
    "startIndex": 104,
  },
  {
    "$event": "attribute",
    "data": [
      "xmlns:ev",
      "http://purl.org/rss/1.0/modules/event/",
      """,
    ],
    "endIndex": 186,
    "startIndex": 137,
  },
  {
    "$event": "attribute",
    "data": [
      "xmlns:content",
      "http://purl.org/rss/1.0/modules/content/",
      """,
    ],
    "endIndex": 243,
    "startIndex": 187,
  },
  {
    "$event": "attribute",
    "data": [
      "xmlns:taxo",
      "http://purl.org/rss/1.0/modules/taxonomy/",
      """,
    ],
    "endIndex": 298,
    "startIndex": 244,
  },
  {
    "$event": "attribute",
    "data": [
      "xmlns:dc",
      "http://purl.org/dc/elements/1.1/",
      """,
    ],
    "endIndex": 342,
    "startIndex": 299,
  },
  {
    "$event": "attribute",
    "data": [
      "xmlns:syn",
      "http://purl.org/rss/1.0/modules/syndication/",
      """,
    ],
    "endIndex": 399,
    "startIndex": 343,
  },
  {
    "$event": "attribute",
    "data": [
      "xmlns:dcterms",
      "http://purl.org/dc/terms/",
      """,
    ],
    "endIndex": 441,
    "startIndex": 400,
  },
  {
    "$event": "attribute",
    "data": [
      "xmlns:admin",
      "http://webns.net/mvcb/",
      """,
    ],
    "endIndex": 478,
    "startIndex": 442,
  },
  {
    "$event": "opentag",
    "data": [
      "rdf:RDF",
      {
        "xmlns": "http://purl.org/rss/1.0/",
        "xmlns:admin": "http://webns.net/mvcb/",
        "xmlns:content": "http://purl.org/rss/1.0/modules/content/",
        "xmlns:dc": "http://purl.org/dc/elements/1.1/",
        "xmlns:dcterms": "http://purl.org/dc/terms/",
        "xmlns:ev": "http://purl.org/rss/1.0/modules/event/",
        "xmlns:rdf": "http://www.w3.org/1999/02/22-rdf-syntax-ns#",
        "xmlns:syn": "http://purl.org/rss/1.0/modules/syndication/",
        "xmlns:taxo": "http://purl.org/rss/1.0/modules/taxonomy/",
      },
      false,
    ],
    "endIndex": 478,
    "startIndex": 39,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 480,
    "startIndex": 479,
  },
  {
    "$event": "opentagname",
    "data": [
      "channel",
    ],
    "endIndex": 489,
    "startIndex": 481,
  },
  {
    "$event": "attribute",
    "data": [
      "rdf:about",
      "https://github.com/fb55/htmlparser2/",
      """,
    ],
    "endIndex": 538,
    "startIndex": 490,
  },
  {
    "$event": "opentag",
    "data": [
      "channel",
      {
        "rdf:about": "https://github.com/fb55/htmlparser2/",
      },
      false,
    ],
    "endIndex": 538,
    "startIndex": 481,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 541,
    "startIndex": 539,
  },
  {
    "$event": "opentagname",
    "data": [
      "title",
    ],
    "endIndex": 548,
    "startIndex": 542,
  },
  {
    "$event": "opentag",
    "data": [
      "title",
      {},
      false,
    ],
    "endIndex": 548,
    "startIndex": 542,
  },
  {
    "$event": "text",
    "data": [
      "A title to parse and remember",
    ],
    "endIndex": 577,
    "startIndex": 549,
  },
  {
    "$event": "closetag",
    "data": [
      "title",
      false,
    ],
    "endIndex": 585,
    "startIndex": 578,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 588,
    "startIndex": 586,
  },
  {
    "$event": "opentagname",
    "data": [
      "link",
    ],
    "endIndex": 594,
    "startIndex": 589,
  },
  {
    "$event": "opentag",
    "data": [
      "link",
      {},
      false,
    ],
    "endIndex": 594,
    "startIndex": 589,
  },
  {
    "$event": "text",
    "data": [
      "https://github.com/fb55/htmlparser2/",
    ],
    "endIndex": 630,
    "startIndex": 595,
  },
  {
    "$event": "closetag",
    "data": [
      "link",
      false,
    ],
    "endIndex": 637,
    "startIndex": 631,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 640,
    "startIndex": 638,
  },
  {
    "$event": "opentagname",
    "data": [
      "description",
    ],
    "endIndex": 653,
    "startIndex": 641,
  },
  {
    "$event": "opentag",
    "data": [
      "description",
      {},
      false,
    ],
    "endIndex": 654,
    "startIndex": 641,
  },
  {
    "$event": "closetag",
    "data": [
      "description",
      true,
    ],
    "endIndex": 654,
    "startIndex": 641,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 657,
    "startIndex": 655,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:language",
    ],
    "endIndex": 670,
    "startIndex": 658,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:language",
      {},
      false,
    ],
    "endIndex": 670,
    "startIndex": 658,
  },
  {
    "$event": "text",
    "data": [
      "en-us",
    ],
    "endIndex": 675,
    "startIndex": 671,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:language",
      false,
    ],
    "endIndex": 689,
    "startIndex": 676,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 692,
    "startIndex": 690,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:rights",
    ],
    "endIndex": 703,
    "startIndex": 693,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:rights",
      {},
      false,
    ],
    "endIndex": 703,
    "startIndex": 693,
  },
  {
    "$event": "text",
    "data": [
      "Copyright 2015 the authors",
    ],
    "endIndex": 729,
    "startIndex": 704,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:rights",
      false,
    ],
    "endIndex": 741,
    "startIndex": 730,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 744,
    "startIndex": 742,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:publisher",
    ],
    "endIndex": 758,
    "startIndex": 745,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:publisher",
      {},
      false,
    ],
    "endIndex": 758,
    "startIndex": 745,
  },
  {
    "$event": "text",
    "data": [
      "<EMAIL>",
    ],
    "endIndex": 786,
    "startIndex": 759,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:publisher",
      false,
    ],
    "endIndex": 801,
    "startIndex": 787,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 804,
    "startIndex": 802,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:creator",
    ],
    "endIndex": 816,
    "startIndex": 805,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:creator",
      {},
      false,
    ],
    "endIndex": 816,
    "startIndex": 805,
  },
  {
    "$event": "text",
    "data": [
      "<EMAIL>",
    ],
    "endIndex": 844,
    "startIndex": 817,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:creator",
      false,
    ],
    "endIndex": 857,
    "startIndex": 845,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 860,
    "startIndex": 858,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:source",
    ],
    "endIndex": 871,
    "startIndex": 861,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:source",
      {},
      false,
    ],
    "endIndex": 871,
    "startIndex": 861,
  },
  {
    "$event": "text",
    "data": [
      "https://github.com/fb55/htmlparser2/",
    ],
    "endIndex": 907,
    "startIndex": 872,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:source",
      false,
    ],
    "endIndex": 919,
    "startIndex": 908,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 922,
    "startIndex": 920,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:title",
    ],
    "endIndex": 932,
    "startIndex": 923,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:title",
      {},
      false,
    ],
    "endIndex": 932,
    "startIndex": 923,
  },
  {
    "$event": "text",
    "data": [
      "A title to parse and remember",
    ],
    "endIndex": 961,
    "startIndex": 933,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:title",
      false,
    ],
    "endIndex": 972,
    "startIndex": 962,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 975,
    "startIndex": 973,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:type",
    ],
    "endIndex": 984,
    "startIndex": 976,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:type",
      {},
      false,
    ],
    "endIndex": 984,
    "startIndex": 976,
  },
  {
    "$event": "text",
    "data": [
      "Collection",
    ],
    "endIndex": 994,
    "startIndex": 985,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:type",
      false,
    ],
    "endIndex": 1004,
    "startIndex": 995,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 1007,
    "startIndex": 1005,
  },
  {
    "$event": "opentagname",
    "data": [
      "syn:updateBase",
    ],
    "endIndex": 1023,
    "startIndex": 1008,
  },
  {
    "$event": "opentag",
    "data": [
      "syn:updateBase",
      {},
      false,
    ],
    "endIndex": 1023,
    "startIndex": 1008,
  },
  {
    "$event": "text",
    "data": [
      "2011-11-04T09:39:10-07:00",
    ],
    "endIndex": 1048,
    "startIndex": 1024,
  },
  {
    "$event": "closetag",
    "data": [
      "syn:updateBase",
      false,
    ],
    "endIndex": 1065,
    "startIndex": 1049,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 1068,
    "startIndex": 1066,
  },
  {
    "$event": "opentagname",
    "data": [
      "syn:updateFrequency",
    ],
    "endIndex": 1089,
    "startIndex": 1069,
  },
  {
    "$event": "opentag",
    "data": [
      "syn:updateFrequency",
      {},
      false,
    ],
    "endIndex": 1089,
    "startIndex": 1069,
  },
  {
    "$event": "text",
    "data": [
      "4",
    ],
    "endIndex": 1090,
    "startIndex": 1090,
  },
  {
    "$event": "closetag",
    "data": [
      "syn:updateFrequency",
      false,
    ],
    "endIndex": 1112,
    "startIndex": 1091,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 1115,
    "startIndex": 1113,
  },
  {
    "$event": "opentagname",
    "data": [
      "syn:updatePeriod",
    ],
    "endIndex": 1133,
    "startIndex": 1116,
  },
  {
    "$event": "opentag",
    "data": [
      "syn:updatePeriod",
      {},
      false,
    ],
    "endIndex": 1133,
    "startIndex": 1116,
  },
  {
    "$event": "text",
    "data": [
      "hourly",
    ],
    "endIndex": 1139,
    "startIndex": 1134,
  },
  {
    "$event": "closetag",
    "data": [
      "syn:updatePeriod",
      false,
    ],
    "endIndex": 1158,
    "startIndex": 1140,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 1161,
    "startIndex": 1159,
  },
  {
    "$event": "opentagname",
    "data": [
      "items",
    ],
    "endIndex": 1168,
    "startIndex": 1162,
  },
  {
    "$event": "opentag",
    "data": [
      "items",
      {},
      false,
    ],
    "endIndex": 1168,
    "startIndex": 1162,
  },
  {
    "$event": "text",
    "data": [
      "
			",
    ],
    "endIndex": 1172,
    "startIndex": 1169,
  },
  {
    "$event": "opentagname",
    "data": [
      "rdf:Seq",
    ],
    "endIndex": 1181,
    "startIndex": 1173,
  },
  {
    "$event": "opentag",
    "data": [
      "rdf:Seq",
      {},
      false,
    ],
    "endIndex": 1181,
    "startIndex": 1173,
  },
  {
    "$event": "text",
    "data": [
      "
				",
    ],
    "endIndex": 1186,
    "startIndex": 1182,
  },
  {
    "$event": "opentagname",
    "data": [
      "rdf:li",
    ],
    "endIndex": 1194,
    "startIndex": 1187,
  },
  {
    "$event": "attribute",
    "data": [
      "rdf:resource",
      "http://somefakesite/path/to/something.html",
      """,
    ],
    "endIndex": 1252,
    "startIndex": 1195,
  },
  {
    "$event": "opentag",
    "data": [
      "rdf:li",
      {
        "rdf:resource": "http://somefakesite/path/to/something.html",
      },
      false,
    ],
    "endIndex": 1253,
    "startIndex": 1187,
  },
  {
    "$event": "closetag",
    "data": [
      "rdf:li",
      true,
    ],
    "endIndex": 1253,
    "startIndex": 1187,
  },
  {
    "$event": "text",
    "data": [
      "
			",
    ],
    "endIndex": 1257,
    "startIndex": 1254,
  },
  {
    "$event": "closetag",
    "data": [
      "rdf:Seq",
      false,
    ],
    "endIndex": 1267,
    "startIndex": 1258,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 1270,
    "startIndex": 1268,
  },
  {
    "$event": "closetag",
    "data": [
      "items",
      false,
    ],
    "endIndex": 1278,
    "startIndex": 1271,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 1280,
    "startIndex": 1279,
  },
  {
    "$event": "closetag",
    "data": [
      "channel",
      false,
    ],
    "endIndex": 1290,
    "startIndex": 1281,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 1292,
    "startIndex": 1291,
  },
  {
    "$event": "opentagname",
    "data": [
      "item",
    ],
    "endIndex": 1298,
    "startIndex": 1293,
  },
  {
    "$event": "attribute",
    "data": [
      "rdf:about",
      "http://somefakesite/path/to/something.html",
      """,
    ],
    "endIndex": 1353,
    "startIndex": 1299,
  },
  {
    "$event": "opentag",
    "data": [
      "item",
      {
        "rdf:about": "http://somefakesite/path/to/something.html",
      },
      false,
    ],
    "endIndex": 1353,
    "startIndex": 1293,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 1356,
    "startIndex": 1354,
  },
  {
    "$event": "opentagname",
    "data": [
      "title",
    ],
    "endIndex": 1363,
    "startIndex": 1357,
  },
  {
    "$event": "opentag",
    "data": [
      "title",
      {},
      false,
    ],
    "endIndex": 1363,
    "startIndex": 1357,
  },
  {
    "$event": "cdatastart",
    "data": [],
    "endIndex": 1394,
    "startIndex": 1364,
  },
  {
    "$event": "text",
    "data": [
      " Fast HTML Parsing ",
    ],
    "endIndex": 1394,
    "startIndex": 1364,
  },
  {
    "$event": "cdataend",
    "data": [],
    "endIndex": 1394,
    "startIndex": 1364,
  },
  {
    "$event": "closetag",
    "data": [
      "title",
      false,
    ],
    "endIndex": 1402,
    "startIndex": 1395,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 1405,
    "startIndex": 1403,
  },
  {
    "$event": "opentagname",
    "data": [
      "link",
    ],
    "endIndex": 1411,
    "startIndex": 1406,
  },
  {
    "$event": "opentag",
    "data": [
      "link",
      {},
      false,
    ],
    "endIndex": 1411,
    "startIndex": 1406,
  },
  {
    "$event": "text",
    "data": [
      "
http://somefakesite/path/to/something.html
",
    ],
    "endIndex": 1455,
    "startIndex": 1412,
  },
  {
    "$event": "closetag",
    "data": [
      "link",
      false,
    ],
    "endIndex": 1462,
    "startIndex": 1456,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 1465,
    "startIndex": 1463,
  },
  {
    "$event": "opentagname",
    "data": [
      "description",
    ],
    "endIndex": 1478,
    "startIndex": 1466,
  },
  {
    "$event": "opentag",
    "data": [
      "description",
      {},
      false,
    ],
    "endIndex": 1478,
    "startIndex": 1466,
  },
  {
    "$event": "cdatastart",
    "data": [],
    "endIndex": 1560,
    "startIndex": 1479,
  },
  {
    "$event": "text",
    "data": [
      "
Great test content<br>A link: <a href="http://github.com">Github</a>
",
    ],
    "endIndex": 1560,
    "startIndex": 1479,
  },
  {
    "$event": "cdataend",
    "data": [],
    "endIndex": 1560,
    "startIndex": 1479,
  },
  {
    "$event": "closetag",
    "data": [
      "description",
      false,
    ],
    "endIndex": 1574,
    "startIndex": 1561,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 1577,
    "startIndex": 1575,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:date",
    ],
    "endIndex": 1586,
    "startIndex": 1578,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:date",
      {},
      false,
    ],
    "endIndex": 1586,
    "startIndex": 1578,
  },
  {
    "$event": "text",
    "data": [
      "2011-11-04T09:35:17-07:00",
    ],
    "endIndex": 1611,
    "startIndex": 1587,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:date",
      false,
    ],
    "endIndex": 1621,
    "startIndex": 1612,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 1624,
    "startIndex": 1622,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:language",
    ],
    "endIndex": 1637,
    "startIndex": 1625,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:language",
      {},
      false,
    ],
    "endIndex": 1637,
    "startIndex": 1625,
  },
  {
    "$event": "text",
    "data": [
      "en-us",
    ],
    "endIndex": 1642,
    "startIndex": 1638,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:language",
      false,
    ],
    "endIndex": 1656,
    "startIndex": 1643,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 1659,
    "startIndex": 1657,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:rights",
    ],
    "endIndex": 1670,
    "startIndex": 1660,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:rights",
      {},
      false,
    ],
    "endIndex": 1670,
    "startIndex": 1660,
  },
  {
    "$event": "text",
    "data": [
      "Copyright 2015 the authors",
    ],
    "endIndex": 1696,
    "startIndex": 1671,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:rights",
      false,
    ],
    "endIndex": 1708,
    "startIndex": 1697,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 1711,
    "startIndex": 1709,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:source",
    ],
    "endIndex": 1722,
    "startIndex": 1712,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:source",
      {},
      false,
    ],
    "endIndex": 1722,
    "startIndex": 1712,
  },
  {
    "$event": "text",
    "data": [
      "
http://somefakesite/path/to/something.html
",
    ],
    "endIndex": 1766,
    "startIndex": 1723,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:source",
      false,
    ],
    "endIndex": 1778,
    "startIndex": 1767,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 1781,
    "startIndex": 1779,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:title",
    ],
    "endIndex": 1791,
    "startIndex": 1782,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:title",
      {},
      false,
    ],
    "endIndex": 1791,
    "startIndex": 1782,
  },
  {
    "$event": "cdatastart",
    "data": [],
    "endIndex": 1822,
    "startIndex": 1792,
  },
  {
    "$event": "text",
    "data": [
      " Fast HTML Parsing ",
    ],
    "endIndex": 1822,
    "startIndex": 1792,
  },
  {
    "$event": "cdataend",
    "data": [],
    "endIndex": 1822,
    "startIndex": 1792,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:title",
      false,
    ],
    "endIndex": 1833,
    "startIndex": 1823,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 1836,
    "startIndex": 1834,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:type",
    ],
    "endIndex": 1845,
    "startIndex": 1837,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:type",
      {},
      false,
    ],
    "endIndex": 1845,
    "startIndex": 1837,
  },
  {
    "$event": "text",
    "data": [
      "text",
    ],
    "endIndex": 1849,
    "startIndex": 1846,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:type",
      false,
    ],
    "endIndex": 1859,
    "startIndex": 1850,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 1862,
    "startIndex": 1860,
  },
  {
    "$event": "opentagname",
    "data": [
      "dcterms:issued",
    ],
    "endIndex": 1878,
    "startIndex": 1863,
  },
  {
    "$event": "opentag",
    "data": [
      "dcterms:issued",
      {},
      false,
    ],
    "endIndex": 1878,
    "startIndex": 1863,
  },
  {
    "$event": "text",
    "data": [
      "2011-11-04T09:35:17-07:00",
    ],
    "endIndex": 1903,
    "startIndex": 1879,
  },
  {
    "$event": "closetag",
    "data": [
      "dcterms:issued",
      false,
    ],
    "endIndex": 1920,
    "startIndex": 1904,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 1922,
    "startIndex": 1921,
  },
  {
    "$event": "closetag",
    "data": [
      "item",
      false,
    ],
    "endIndex": 1929,
    "startIndex": 1923,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 1931,
    "startIndex": 1930,
  },
  {
    "$event": "opentagname",
    "data": [
      "item",
    ],
    "endIndex": 1937,
    "startIndex": 1932,
  },
  {
    "$event": "attribute",
    "data": [
      "rdf:about",
      "http://somefakesite/path/to/something-else.html",
      """,
    ],
    "endIndex": 1997,
    "startIndex": 1938,
  },
  {
    "$event": "opentag",
    "data": [
      "item",
      {
        "rdf:about": "http://somefakesite/path/to/something-else.html",
      },
      false,
    ],
    "endIndex": 1997,
    "startIndex": 1932,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 2000,
    "startIndex": 1998,
  },
  {
    "$event": "opentagname",
    "data": [
      "title",
    ],
    "endIndex": 2007,
    "startIndex": 2001,
  },
  {
    "$event": "opentag",
    "data": [
      "title",
      {},
      false,
    ],
    "endIndex": 2007,
    "startIndex": 2001,
  },
  {
    "$event": "cdatastart",
    "data": [],
    "endIndex": 2056,
    "startIndex": 2008,
  },
  {
    "$event": "text",
    "data": [
      "
This space intentionally left blank
",
    ],
    "endIndex": 2056,
    "startIndex": 2008,
  },
  {
    "$event": "cdataend",
    "data": [],
    "endIndex": 2056,
    "startIndex": 2008,
  },
  {
    "$event": "closetag",
    "data": [
      "title",
      false,
    ],
    "endIndex": 2064,
    "startIndex": 2057,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 2067,
    "startIndex": 2065,
  },
  {
    "$event": "opentagname",
    "data": [
      "link",
    ],
    "endIndex": 2073,
    "startIndex": 2068,
  },
  {
    "$event": "opentag",
    "data": [
      "link",
      {},
      false,
    ],
    "endIndex": 2073,
    "startIndex": 2068,
  },
  {
    "$event": "text",
    "data": [
      "
http://somefakesite/path/to/something-else.html
",
    ],
    "endIndex": 2122,
    "startIndex": 2074,
  },
  {
    "$event": "closetag",
    "data": [
      "link",
      false,
    ],
    "endIndex": 2129,
    "startIndex": 2123,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 2132,
    "startIndex": 2130,
  },
  {
    "$event": "opentagname",
    "data": [
      "description",
    ],
    "endIndex": 2145,
    "startIndex": 2133,
  },
  {
    "$event": "opentag",
    "data": [
      "description",
      {},
      false,
    ],
    "endIndex": 2145,
    "startIndex": 2133,
  },
  {
    "$event": "cdatastart",
    "data": [],
    "endIndex": 2187,
    "startIndex": 2146,
  },
  {
    "$event": "text",
    "data": [
      "
The early bird gets the worm
",
    ],
    "endIndex": 2187,
    "startIndex": 2146,
  },
  {
    "$event": "cdataend",
    "data": [],
    "endIndex": 2187,
    "startIndex": 2146,
  },
  {
    "$event": "closetag",
    "data": [
      "description",
      false,
    ],
    "endIndex": 2201,
    "startIndex": 2188,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 2204,
    "startIndex": 2202,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:date",
    ],
    "endIndex": 2213,
    "startIndex": 2205,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:date",
      {},
      false,
    ],
    "endIndex": 2213,
    "startIndex": 2205,
  },
  {
    "$event": "text",
    "data": [
      "2011-11-04T09:34:54-07:00",
    ],
    "endIndex": 2238,
    "startIndex": 2214,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:date",
      false,
    ],
    "endIndex": 2248,
    "startIndex": 2239,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 2251,
    "startIndex": 2249,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:language",
    ],
    "endIndex": 2264,
    "startIndex": 2252,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:language",
      {},
      false,
    ],
    "endIndex": 2264,
    "startIndex": 2252,
  },
  {
    "$event": "text",
    "data": [
      "en-us",
    ],
    "endIndex": 2269,
    "startIndex": 2265,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:language",
      false,
    ],
    "endIndex": 2283,
    "startIndex": 2270,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 2286,
    "startIndex": 2284,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:rights",
    ],
    "endIndex": 2297,
    "startIndex": 2287,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:rights",
      {},
      false,
    ],
    "endIndex": 2297,
    "startIndex": 2287,
  },
  {
    "$event": "text",
    "data": [
      "Copyright 2015 the authors",
    ],
    "endIndex": 2323,
    "startIndex": 2298,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:rights",
      false,
    ],
    "endIndex": 2335,
    "startIndex": 2324,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 2338,
    "startIndex": 2336,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:source",
    ],
    "endIndex": 2349,
    "startIndex": 2339,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:source",
      {},
      false,
    ],
    "endIndex": 2349,
    "startIndex": 2339,
  },
  {
    "$event": "text",
    "data": [
      "
http://somefakesite/path/to/something-else.html
",
    ],
    "endIndex": 2398,
    "startIndex": 2350,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:source",
      false,
    ],
    "endIndex": 2410,
    "startIndex": 2399,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 2413,
    "startIndex": 2411,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:title",
    ],
    "endIndex": 2423,
    "startIndex": 2414,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:title",
      {},
      false,
    ],
    "endIndex": 2423,
    "startIndex": 2414,
  },
  {
    "$event": "cdatastart",
    "data": [],
    "endIndex": 2472,
    "startIndex": 2424,
  },
  {
    "$event": "text",
    "data": [
      "
This space intentionally left blank
",
    ],
    "endIndex": 2472,
    "startIndex": 2424,
  },
  {
    "$event": "cdataend",
    "data": [],
    "endIndex": 2472,
    "startIndex": 2424,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:title",
      false,
    ],
    "endIndex": 2483,
    "startIndex": 2473,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 2486,
    "startIndex": 2484,
  },
  {
    "$event": "opentagname",
    "data": [
      "dc:type",
    ],
    "endIndex": 2495,
    "startIndex": 2487,
  },
  {
    "$event": "opentag",
    "data": [
      "dc:type",
      {},
      false,
    ],
    "endIndex": 2495,
    "startIndex": 2487,
  },
  {
    "$event": "text",
    "data": [
      "text",
    ],
    "endIndex": 2499,
    "startIndex": 2496,
  },
  {
    "$event": "closetag",
    "data": [
      "dc:type",
      false,
    ],
    "endIndex": 2509,
    "startIndex": 2500,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 2512,
    "startIndex": 2510,
  },
  {
    "$event": "opentagname",
    "data": [
      "dcterms:issued",
    ],
    "endIndex": 2528,
    "startIndex": 2513,
  },
  {
    "$event": "opentag",
    "data": [
      "dcterms:issued",
      {},
      false,
    ],
    "endIndex": 2528,
    "startIndex": 2513,
  },
  {
    "$event": "text",
    "data": [
      "2011-11-04T09:34:54-07:00",
    ],
    "endIndex": 2553,
    "startIndex": 2529,
  },
  {
    "$event": "closetag",
    "data": [
      "dcterms:issued",
      false,
    ],
    "endIndex": 2570,
    "startIndex": 2554,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 2572,
    "startIndex": 2571,
  },
  {
    "$event": "closetag",
    "data": [
      "item",
      false,
    ],
    "endIndex": 2579,
    "startIndex": 2573,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 2580,
    "startIndex": 2580,
  },
  {
    "$event": "closetag",
    "data": [
      "rdf:RDF",
      false,
    ],
    "endIndex": 2590,
    "startIndex": 2581,
  },
]
`;

exports[`WritableStream > RSS feed 1`] = `
[
  {
    "$event": "processinginstruction",
    "data": [
      "?xml",
      "?xml version="1.0"?",
    ],
    "endIndex": 20,
    "startIndex": 0,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 21,
    "startIndex": 21,
  },
  {
    "$event": "comment",
    "data": [
      " http://cyber.law.harvard.edu/rss/examples/rss2sample.xml ",
    ],
    "endIndex": 86,
    "startIndex": 22,
  },
  {
    "$event": "commentend",
    "data": [],
    "endIndex": 86,
    "startIndex": 22,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 87,
    "startIndex": 87,
  },
  {
    "$event": "opentagname",
    "data": [
      "rss",
    ],
    "endIndex": 92,
    "startIndex": 88,
  },
  {
    "$event": "attribute",
    "data": [
      "version",
      "2.0",
      """,
    ],
    "endIndex": 106,
    "startIndex": 93,
  },
  {
    "$event": "opentag",
    "data": [
      "rss",
      {
        "version": "2.0",
      },
      false,
    ],
    "endIndex": 106,
    "startIndex": 88,
  },
  {
    "$event": "text",
    "data": [
      "
   ",
    ],
    "endIndex": 110,
    "startIndex": 107,
  },
  {
    "$event": "opentagname",
    "data": [
      "channel",
    ],
    "endIndex": 119,
    "startIndex": 111,
  },
  {
    "$event": "opentag",
    "data": [
      "channel",
      {},
      false,
    ],
    "endIndex": 119,
    "startIndex": 111,
  },
  {
    "$event": "text",
    "data": [
      "
      ",
    ],
    "endIndex": 126,
    "startIndex": 120,
  },
  {
    "$event": "opentagname",
    "data": [
      "title",
    ],
    "endIndex": 133,
    "startIndex": 127,
  },
  {
    "$event": "opentag",
    "data": [
      "title",
      {},
      false,
    ],
    "endIndex": 133,
    "startIndex": 127,
  },
  {
    "$event": "text",
    "data": [
      "Liftoff News",
    ],
    "endIndex": 145,
    "startIndex": 134,
  },
  {
    "$event": "closetag",
    "data": [
      "title",
      false,
    ],
    "endIndex": 153,
    "startIndex": 146,
  },
  {
    "$event": "text",
    "data": [
      "
      ",
    ],
    "endIndex": 160,
    "startIndex": 154,
  },
  {
    "$event": "opentagname",
    "data": [
      "link",
    ],
    "endIndex": 166,
    "startIndex": 161,
  },
  {
    "$event": "opentag",
    "data": [
      "link",
      {},
      false,
    ],
    "endIndex": 166,
    "startIndex": 161,
  },
  {
    "$event": "text",
    "data": [
      "http://liftoff.msfc.nasa.gov/",
    ],
    "endIndex": 195,
    "startIndex": 167,
  },
  {
    "$event": "closetag",
    "data": [
      "link",
      false,
    ],
    "endIndex": 202,
    "startIndex": 196,
  },
  {
    "$event": "text",
    "data": [
      "
      ",
    ],
    "endIndex": 209,
    "startIndex": 203,
  },
  {
    "$event": "opentagname",
    "data": [
      "description",
    ],
    "endIndex": 222,
    "startIndex": 210,
  },
  {
    "$event": "opentag",
    "data": [
      "description",
      {},
      false,
    ],
    "endIndex": 222,
    "startIndex": 210,
  },
  {
    "$event": "text",
    "data": [
      "Liftoff to Space Exploration.",
    ],
    "endIndex": 251,
    "startIndex": 223,
  },
  {
    "$event": "closetag",
    "data": [
      "description",
      false,
    ],
    "endIndex": 265,
    "startIndex": 252,
  },
  {
    "$event": "text",
    "data": [
      "
      ",
    ],
    "endIndex": 272,
    "startIndex": 266,
  },
  {
    "$event": "opentagname",
    "data": [
      "language",
    ],
    "endIndex": 282,
    "startIndex": 273,
  },
  {
    "$event": "opentag",
    "data": [
      "language",
      {},
      false,
    ],
    "endIndex": 282,
    "startIndex": 273,
  },
  {
    "$event": "text",
    "data": [
      "en-us",
    ],
    "endIndex": 287,
    "startIndex": 283,
  },
  {
    "$event": "closetag",
    "data": [
      "language",
      false,
    ],
    "endIndex": 298,
    "startIndex": 288,
  },
  {
    "$event": "text",
    "data": [
      "
      ",
    ],
    "endIndex": 305,
    "startIndex": 299,
  },
  {
    "$event": "opentagname",
    "data": [
      "pubDate",
    ],
    "endIndex": 314,
    "startIndex": 306,
  },
  {
    "$event": "opentag",
    "data": [
      "pubDate",
      {},
      false,
    ],
    "endIndex": 314,
    "startIndex": 306,
  },
  {
    "$event": "text",
    "data": [
      "Tue, 10 Jun 2003 04:00:00 GMT",
    ],
    "endIndex": 343,
    "startIndex": 315,
  },
  {
    "$event": "closetag",
    "data": [
      "pubDate",
      false,
    ],
    "endIndex": 353,
    "startIndex": 344,
  },
  {
    "$event": "text",
    "data": [
      "

      ",
    ],
    "endIndex": 361,
    "startIndex": 354,
  },
  {
    "$event": "opentagname",
    "data": [
      "lastBuildDate",
    ],
    "endIndex": 376,
    "startIndex": 362,
  },
  {
    "$event": "opentag",
    "data": [
      "lastBuildDate",
      {},
      false,
    ],
    "endIndex": 376,
    "startIndex": 362,
  },
  {
    "$event": "text",
    "data": [
      "Tue, 10 Jun 2003 09:41:01 GMT",
    ],
    "endIndex": 405,
    "startIndex": 377,
  },
  {
    "$event": "closetag",
    "data": [
      "lastBuildDate",
      false,
    ],
    "endIndex": 421,
    "startIndex": 406,
  },
  {
    "$event": "text",
    "data": [
      "
      ",
    ],
    "endIndex": 428,
    "startIndex": 422,
  },
  {
    "$event": "opentagname",
    "data": [
      "docs",
    ],
    "endIndex": 434,
    "startIndex": 429,
  },
  {
    "$event": "opentag",
    "data": [
      "docs",
      {},
      false,
    ],
    "endIndex": 434,
    "startIndex": 429,
  },
  {
    "$event": "text",
    "data": [
      "http://blogs.law.harvard.edu/tech/rss",
    ],
    "endIndex": 471,
    "startIndex": 435,
  },
  {
    "$event": "closetag",
    "data": [
      "docs",
      false,
    ],
    "endIndex": 478,
    "startIndex": 472,
  },
  {
    "$event": "text",
    "data": [
      "
      ",
    ],
    "endIndex": 485,
    "startIndex": 479,
  },
  {
    "$event": "opentagname",
    "data": [
      "generator",
    ],
    "endIndex": 496,
    "startIndex": 486,
  },
  {
    "$event": "opentag",
    "data": [
      "generator",
      {},
      false,
    ],
    "endIndex": 496,
    "startIndex": 486,
  },
  {
    "$event": "text",
    "data": [
      "Weblog Editor 2.0",
    ],
    "endIndex": 513,
    "startIndex": 497,
  },
  {
    "$event": "closetag",
    "data": [
      "generator",
      false,
    ],
    "endIndex": 525,
    "startIndex": 514,
  },
  {
    "$event": "text",
    "data": [
      "
      ",
    ],
    "endIndex": 532,
    "startIndex": 526,
  },
  {
    "$event": "opentagname",
    "data": [
      "managingEditor",
    ],
    "endIndex": 548,
    "startIndex": 533,
  },
  {
    "$event": "opentag",
    "data": [
      "managingEditor",
      {},
      false,
    ],
    "endIndex": 548,
    "startIndex": 533,
  },
  {
    "$event": "text",
    "data": [
      "<EMAIL>",
    ],
    "endIndex": 566,
    "startIndex": 549,
  },
  {
    "$event": "closetag",
    "data": [
      "managingEditor",
      false,
    ],
    "endIndex": 583,
    "startIndex": 567,
  },
  {
    "$event": "text",
    "data": [
      "
      ",
    ],
    "endIndex": 590,
    "startIndex": 584,
  },
  {
    "$event": "opentagname",
    "data": [
      "webMaster",
    ],
    "endIndex": 601,
    "startIndex": 591,
  },
  {
    "$event": "opentag",
    "data": [
      "webMaster",
      {},
      false,
    ],
    "endIndex": 601,
    "startIndex": 591,
  },
  {
    "$event": "text",
    "data": [
      "<EMAIL>",
    ],
    "endIndex": 622,
    "startIndex": 602,
  },
  {
    "$event": "closetag",
    "data": [
      "webMaster",
      false,
    ],
    "endIndex": 634,
    "startIndex": 623,
  },
  {
    "$event": "text",
    "data": [
      "
      ",
    ],
    "endIndex": 641,
    "startIndex": 635,
  },
  {
    "$event": "opentagname",
    "data": [
      "item",
    ],
    "endIndex": 647,
    "startIndex": 642,
  },
  {
    "$event": "opentag",
    "data": [
      "item",
      {},
      false,
    ],
    "endIndex": 647,
    "startIndex": 642,
  },
  {
    "$event": "text",
    "data": [
      "

         ",
    ],
    "endIndex": 658,
    "startIndex": 648,
  },
  {
    "$event": "opentagname",
    "data": [
      "title",
    ],
    "endIndex": 665,
    "startIndex": 659,
  },
  {
    "$event": "opentag",
    "data": [
      "title",
      {},
      false,
    ],
    "endIndex": 665,
    "startIndex": 659,
  },
  {
    "$event": "text",
    "data": [
      "Star City",
    ],
    "endIndex": 674,
    "startIndex": 666,
  },
  {
    "$event": "closetag",
    "data": [
      "title",
      false,
    ],
    "endIndex": 682,
    "startIndex": 675,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 692,
    "startIndex": 683,
  },
  {
    "$event": "opentagname",
    "data": [
      "link",
    ],
    "endIndex": 698,
    "startIndex": 693,
  },
  {
    "$event": "opentag",
    "data": [
      "link",
      {},
      false,
    ],
    "endIndex": 698,
    "startIndex": 693,
  },
  {
    "$event": "text",
    "data": [
      "http://liftoff.msfc.nasa.gov/news/2003/news-starcity.asp",
    ],
    "endIndex": 754,
    "startIndex": 699,
  },
  {
    "$event": "closetag",
    "data": [
      "link",
      false,
    ],
    "endIndex": 761,
    "startIndex": 755,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 771,
    "startIndex": 762,
  },
  {
    "$event": "opentagname",
    "data": [
      "description",
    ],
    "endIndex": 784,
    "startIndex": 772,
  },
  {
    "$event": "opentag",
    "data": [
      "description",
      {},
      false,
    ],
    "endIndex": 784,
    "startIndex": 772,
  },
  {
    "$event": "text",
    "data": [
      "How do Americans get ready to work with Russians aboard the International Space Station? They take a crash course in culture, language and protocol at Russia's <a href="http://howe.iki.rssi.ru/GCTC/gctc_e.htm">Star City</a>.",
    ],
    "endIndex": 1020,
    "startIndex": 785,
  },
  {
    "$event": "closetag",
    "data": [
      "description",
      false,
    ],
    "endIndex": 1034,
    "startIndex": 1021,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 1044,
    "startIndex": 1035,
  },
  {
    "$event": "opentagname",
    "data": [
      "pubDate",
    ],
    "endIndex": 1053,
    "startIndex": 1045,
  },
  {
    "$event": "opentag",
    "data": [
      "pubDate",
      {},
      false,
    ],
    "endIndex": 1053,
    "startIndex": 1045,
  },
  {
    "$event": "text",
    "data": [
      "Tue, 03 Jun 2003 09:39:21 GMT",
    ],
    "endIndex": 1082,
    "startIndex": 1054,
  },
  {
    "$event": "closetag",
    "data": [
      "pubDate",
      false,
    ],
    "endIndex": 1092,
    "startIndex": 1083,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 1102,
    "startIndex": 1093,
  },
  {
    "$event": "opentagname",
    "data": [
      "guid",
    ],
    "endIndex": 1108,
    "startIndex": 1103,
  },
  {
    "$event": "opentag",
    "data": [
      "guid",
      {},
      false,
    ],
    "endIndex": 1108,
    "startIndex": 1103,
  },
  {
    "$event": "text",
    "data": [
      "http://liftoff.msfc.nasa.gov/2003/06/03.html#item573",
    ],
    "endIndex": 1160,
    "startIndex": 1109,
  },
  {
    "$event": "closetag",
    "data": [
      "guid",
      false,
    ],
    "endIndex": 1167,
    "startIndex": 1161,
  },
  {
    "$event": "text",
    "data": [
      "

      ",
    ],
    "endIndex": 1175,
    "startIndex": 1168,
  },
  {
    "$event": "closetag",
    "data": [
      "item",
      false,
    ],
    "endIndex": 1182,
    "startIndex": 1176,
  },
  {
    "$event": "text",
    "data": [
      "
      ",
    ],
    "endIndex": 1189,
    "startIndex": 1183,
  },
  {
    "$event": "opentagname",
    "data": [
      "item",
    ],
    "endIndex": 1195,
    "startIndex": 1190,
  },
  {
    "$event": "opentag",
    "data": [
      "item",
      {},
      false,
    ],
    "endIndex": 1195,
    "startIndex": 1190,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 1205,
    "startIndex": 1196,
  },
  {
    "$event": "opentagname",
    "data": [
      "description",
    ],
    "endIndex": 1218,
    "startIndex": 1206,
  },
  {
    "$event": "opentag",
    "data": [
      "description",
      {},
      false,
    ],
    "endIndex": 1218,
    "startIndex": 1206,
  },
  {
    "$event": "text",
    "data": [
      "Sky watchers in Europe, Asia, and parts of Alaska and Canada will experience a <a href="http://science.nasa.gov/headlines/y2003/30may_solareclipse.htm">partial eclipse of the Sun</a> on Saturday, May 31st.",
    ],
    "endIndex": 1435,
    "startIndex": 1219,
  },
  {
    "$event": "closetag",
    "data": [
      "description",
      false,
    ],
    "endIndex": 1449,
    "startIndex": 1436,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 1459,
    "startIndex": 1450,
  },
  {
    "$event": "opentagname",
    "data": [
      "pubDate",
    ],
    "endIndex": 1468,
    "startIndex": 1460,
  },
  {
    "$event": "opentag",
    "data": [
      "pubDate",
      {},
      false,
    ],
    "endIndex": 1468,
    "startIndex": 1460,
  },
  {
    "$event": "text",
    "data": [
      "Fri, 30 May 2003 11:06:42 GMT",
    ],
    "endIndex": 1497,
    "startIndex": 1469,
  },
  {
    "$event": "closetag",
    "data": [
      "pubDate",
      false,
    ],
    "endIndex": 1507,
    "startIndex": 1498,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 1517,
    "startIndex": 1508,
  },
  {
    "$event": "opentagname",
    "data": [
      "guid",
    ],
    "endIndex": 1523,
    "startIndex": 1518,
  },
  {
    "$event": "opentag",
    "data": [
      "guid",
      {},
      false,
    ],
    "endIndex": 1523,
    "startIndex": 1518,
  },
  {
    "$event": "text",
    "data": [
      "http://liftoff.msfc.nasa.gov/2003/05/30.html#item572",
    ],
    "endIndex": 1575,
    "startIndex": 1524,
  },
  {
    "$event": "closetag",
    "data": [
      "guid",
      false,
    ],
    "endIndex": 1582,
    "startIndex": 1576,
  },
  {
    "$event": "text",
    "data": [
      "

      ",
    ],
    "endIndex": 1590,
    "startIndex": 1583,
  },
  {
    "$event": "closetag",
    "data": [
      "item",
      false,
    ],
    "endIndex": 1597,
    "startIndex": 1591,
  },
  {
    "$event": "text",
    "data": [
      "
      ",
    ],
    "endIndex": 1604,
    "startIndex": 1598,
  },
  {
    "$event": "opentagname",
    "data": [
      "item",
    ],
    "endIndex": 1610,
    "startIndex": 1605,
  },
  {
    "$event": "opentag",
    "data": [
      "item",
      {},
      false,
    ],
    "endIndex": 1610,
    "startIndex": 1605,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 1620,
    "startIndex": 1611,
  },
  {
    "$event": "opentagname",
    "data": [
      "title",
    ],
    "endIndex": 1627,
    "startIndex": 1621,
  },
  {
    "$event": "opentag",
    "data": [
      "title",
      {},
      false,
    ],
    "endIndex": 1627,
    "startIndex": 1621,
  },
  {
    "$event": "text",
    "data": [
      "The Engine That Does More",
    ],
    "endIndex": 1652,
    "startIndex": 1628,
  },
  {
    "$event": "closetag",
    "data": [
      "title",
      false,
    ],
    "endIndex": 1660,
    "startIndex": 1653,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 1670,
    "startIndex": 1661,
  },
  {
    "$event": "opentagname",
    "data": [
      "link",
    ],
    "endIndex": 1676,
    "startIndex": 1671,
  },
  {
    "$event": "opentag",
    "data": [
      "link",
      {},
      false,
    ],
    "endIndex": 1676,
    "startIndex": 1671,
  },
  {
    "$event": "text",
    "data": [
      "http://liftoff.msfc.nasa.gov/news/2003/news-VASIMR.asp",
    ],
    "endIndex": 1730,
    "startIndex": 1677,
  },
  {
    "$event": "closetag",
    "data": [
      "link",
      false,
    ],
    "endIndex": 1737,
    "startIndex": 1731,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 1747,
    "startIndex": 1738,
  },
  {
    "$event": "opentagname",
    "data": [
      "description",
    ],
    "endIndex": 1760,
    "startIndex": 1748,
  },
  {
    "$event": "opentag",
    "data": [
      "description",
      {},
      false,
    ],
    "endIndex": 1760,
    "startIndex": 1748,
  },
  {
    "$event": "text",
    "data": [
      "Before man travels to Mars, NASA hopes to design new engines that will let us fly through the Solar System more quickly.  The proposed VASIMR engine would do that.",
    ],
    "endIndex": 1923,
    "startIndex": 1761,
  },
  {
    "$event": "closetag",
    "data": [
      "description",
      false,
    ],
    "endIndex": 1937,
    "startIndex": 1924,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 1947,
    "startIndex": 1938,
  },
  {
    "$event": "opentagname",
    "data": [
      "pubDate",
    ],
    "endIndex": 1956,
    "startIndex": 1948,
  },
  {
    "$event": "opentag",
    "data": [
      "pubDate",
      {},
      false,
    ],
    "endIndex": 1956,
    "startIndex": 1948,
  },
  {
    "$event": "text",
    "data": [
      "Tue, 27 May 2003 08:37:32 GMT",
    ],
    "endIndex": 1985,
    "startIndex": 1957,
  },
  {
    "$event": "closetag",
    "data": [
      "pubDate",
      false,
    ],
    "endIndex": 1995,
    "startIndex": 1986,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 2005,
    "startIndex": 1996,
  },
  {
    "$event": "opentagname",
    "data": [
      "guid",
    ],
    "endIndex": 2011,
    "startIndex": 2006,
  },
  {
    "$event": "opentag",
    "data": [
      "guid",
      {},
      false,
    ],
    "endIndex": 2011,
    "startIndex": 2006,
  },
  {
    "$event": "text",
    "data": [
      "http://liftoff.msfc.nasa.gov/2003/05/27.html#item571",
    ],
    "endIndex": 2063,
    "startIndex": 2012,
  },
  {
    "$event": "closetag",
    "data": [
      "guid",
      false,
    ],
    "endIndex": 2070,
    "startIndex": 2064,
  },
  {
    "$event": "text",
    "data": [
      "

      ",
    ],
    "endIndex": 2078,
    "startIndex": 2071,
  },
  {
    "$event": "closetag",
    "data": [
      "item",
      false,
    ],
    "endIndex": 2085,
    "startIndex": 2079,
  },
  {
    "$event": "text",
    "data": [
      "
      ",
    ],
    "endIndex": 2092,
    "startIndex": 2086,
  },
  {
    "$event": "opentagname",
    "data": [
      "item",
    ],
    "endIndex": 2098,
    "startIndex": 2093,
  },
  {
    "$event": "opentag",
    "data": [
      "item",
      {},
      false,
    ],
    "endIndex": 2098,
    "startIndex": 2093,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 2108,
    "startIndex": 2099,
  },
  {
    "$event": "opentagname",
    "data": [
      "title",
    ],
    "endIndex": 2115,
    "startIndex": 2109,
  },
  {
    "$event": "opentag",
    "data": [
      "title",
      {},
      false,
    ],
    "endIndex": 2115,
    "startIndex": 2109,
  },
  {
    "$event": "text",
    "data": [
      "Astronauts' Dirty Laundry",
    ],
    "endIndex": 2140,
    "startIndex": 2116,
  },
  {
    "$event": "closetag",
    "data": [
      "title",
      false,
    ],
    "endIndex": 2148,
    "startIndex": 2141,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 2158,
    "startIndex": 2149,
  },
  {
    "$event": "opentagname",
    "data": [
      "link",
    ],
    "endIndex": 2164,
    "startIndex": 2159,
  },
  {
    "$event": "opentag",
    "data": [
      "link",
      {},
      false,
    ],
    "endIndex": 2164,
    "startIndex": 2159,
  },
  {
    "$event": "text",
    "data": [
      "http://liftoff.msfc.nasa.gov/news/2003/news-laundry.asp",
    ],
    "endIndex": 2219,
    "startIndex": 2165,
  },
  {
    "$event": "closetag",
    "data": [
      "link",
      false,
    ],
    "endIndex": 2226,
    "startIndex": 2220,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 2236,
    "startIndex": 2227,
  },
  {
    "$event": "opentagname",
    "data": [
      "description",
    ],
    "endIndex": 2249,
    "startIndex": 2237,
  },
  {
    "$event": "opentag",
    "data": [
      "description",
      {},
      false,
    ],
    "endIndex": 2249,
    "startIndex": 2237,
  },
  {
    "$event": "text",
    "data": [
      "Compared to earlier spacecraft, the International Space Station has many luxuries, but laundry facilities are not one of them.  Instead, astronauts have other options.",
    ],
    "endIndex": 2416,
    "startIndex": 2250,
  },
  {
    "$event": "closetag",
    "data": [
      "description",
      false,
    ],
    "endIndex": 2430,
    "startIndex": 2417,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 2440,
    "startIndex": 2431,
  },
  {
    "$event": "opentagname",
    "data": [
      "pubDate",
    ],
    "endIndex": 2449,
    "startIndex": 2441,
  },
  {
    "$event": "opentag",
    "data": [
      "pubDate",
      {},
      false,
    ],
    "endIndex": 2449,
    "startIndex": 2441,
  },
  {
    "$event": "text",
    "data": [
      "Tue, 20 May 2003 08:56:02 GMT",
    ],
    "endIndex": 2478,
    "startIndex": 2450,
  },
  {
    "$event": "closetag",
    "data": [
      "pubDate",
      false,
    ],
    "endIndex": 2488,
    "startIndex": 2479,
  },
  {
    "$event": "text",
    "data": [
      "
         ",
    ],
    "endIndex": 2498,
    "startIndex": 2489,
  },
  {
    "$event": "opentagname",
    "data": [
      "guid",
    ],
    "endIndex": 2504,
    "startIndex": 2499,
  },
  {
    "$event": "opentag",
    "data": [
      "guid",
      {},
      false,
    ],
    "endIndex": 2504,
    "startIndex": 2499,
  },
  {
    "$event": "text",
    "data": [
      "http://liftoff.msfc.nasa.gov/2003/05/20.html#item570",
    ],
    "endIndex": 2556,
    "startIndex": 2505,
  },
  {
    "$event": "closetag",
    "data": [
      "guid",
      false,
    ],
    "endIndex": 2563,
    "startIndex": 2557,
  },
  {
    "$event": "text",
    "data": [
      "

         ",
    ],
    "endIndex": 2574,
    "startIndex": 2564,
  },
  {
    "$event": "opentagname",
    "data": [
      "media:content",
    ],
    "endIndex": 2589,
    "startIndex": 2575,
  },
  {
    "$event": "attribute",
    "data": [
      "height",
      "200",
      """,
    ],
    "endIndex": 2602,
    "startIndex": 2590,
  },
  {
    "$event": "attribute",
    "data": [
      "medium",
      "image",
      """,
    ],
    "endIndex": 2617,
    "startIndex": 2603,
  },
  {
    "$event": "attribute",
    "data": [
      "url",
      "https://picsum.photos/200",
      """,
    ],
    "endIndex": 2649,
    "startIndex": 2618,
  },
  {
    "$event": "attribute",
    "data": [
      "width",
      "200",
      """,
    ],
    "endIndex": 2661,
    "startIndex": 2650,
  },
  {
    "$event": "opentag",
    "data": [
      "media:content",
      {
        "height": "200",
        "medium": "image",
        "url": "https://picsum.photos/200",
        "width": "200",
      },
      false,
    ],
    "endIndex": 2662,
    "startIndex": 2575,
  },
  {
    "$event": "closetag",
    "data": [
      "media:content",
      true,
    ],
    "endIndex": 2662,
    "startIndex": 2575,
  },
  {
    "$event": "text",
    "data": [
      "
      ",
    ],
    "endIndex": 2669,
    "startIndex": 2663,
  },
  {
    "$event": "closetag",
    "data": [
      "item",
      false,
    ],
    "endIndex": 2676,
    "startIndex": 2670,
  },
  {
    "$event": "text",
    "data": [
      "
   ",
    ],
    "endIndex": 2680,
    "startIndex": 2677,
  },
  {
    "$event": "closetag",
    "data": [
      "channel",
      false,
    ],
    "endIndex": 2690,
    "startIndex": 2681,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 2691,
    "startIndex": 2691,
  },
  {
    "$event": "closetag",
    "data": [
      "rss",
      false,
    ],
    "endIndex": 2697,
    "startIndex": 2692,
  },
]
`;

exports[`WritableStream > SVG 1`] = `
[
  {
    "$event": "processinginstruction",
    "data": [
      "!doctype",
      "!doctype html",
    ],
    "endIndex": 14,
    "startIndex": 0,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 15,
    "startIndex": 15,
  },
  {
    "$event": "opentagname",
    "data": [
      "html",
    ],
    "endIndex": 21,
    "startIndex": 16,
  },
  {
    "$event": "opentag",
    "data": [
      "html",
      {},
      false,
    ],
    "endIndex": 21,
    "startIndex": 16,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 22,
    "startIndex": 22,
  },
  {
    "$event": "opentagname",
    "data": [
      "head",
    ],
    "endIndex": 28,
    "startIndex": 23,
  },
  {
    "$event": "opentag",
    "data": [
      "head",
      {},
      false,
    ],
    "endIndex": 28,
    "startIndex": 23,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 30,
    "startIndex": 29,
  },
  {
    "$event": "opentagname",
    "data": [
      "title",
    ],
    "endIndex": 37,
    "startIndex": 31,
  },
  {
    "$event": "opentag",
    "data": [
      "title",
      {},
      false,
    ],
    "endIndex": 37,
    "startIndex": 31,
  },
  {
    "$event": "text",
    "data": [
      "SVG test",
    ],
    "endIndex": 45,
    "startIndex": 38,
  },
  {
    "$event": "closetag",
    "data": [
      "title",
      false,
    ],
    "endIndex": 53,
    "startIndex": 46,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 54,
    "startIndex": 54,
  },
  {
    "$event": "closetag",
    "data": [
      "head",
      false,
    ],
    "endIndex": 61,
    "startIndex": 55,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 62,
    "startIndex": 62,
  },
  {
    "$event": "opentagname",
    "data": [
      "body",
    ],
    "endIndex": 68,
    "startIndex": 63,
  },
  {
    "$event": "opentag",
    "data": [
      "body",
      {},
      false,
    ],
    "endIndex": 68,
    "startIndex": 63,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 70,
    "startIndex": 69,
  },
  {
    "$event": "opentagname",
    "data": [
      "svg",
    ],
    "endIndex": 75,
    "startIndex": 71,
  },
  {
    "$event": "attribute",
    "data": [
      "version",
      "1.1",
      """,
    ],
    "endIndex": 89,
    "startIndex": 76,
  },
  {
    "$event": "attribute",
    "data": [
      "xmlns",
      "http://www.w3.org/2000/svg",
      """,
    ],
    "endIndex": 124,
    "startIndex": 90,
  },
  {
    "$event": "attribute",
    "data": [
      "xmlns:xlink",
      "http://www.w3.org/1999/xlink",
      """,
    ],
    "endIndex": 167,
    "startIndex": 125,
  },
  {
    "$event": "opentag",
    "data": [
      "svg",
      {
        "version": "1.1",
        "xmlns": "http://www.w3.org/2000/svg",
        "xmlns:xlink": "http://www.w3.org/1999/xlink",
      },
      false,
    ],
    "endIndex": 167,
    "startIndex": 71,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 170,
    "startIndex": 168,
  },
  {
    "$event": "opentagname",
    "data": [
      "title",
    ],
    "endIndex": 177,
    "startIndex": 171,
  },
  {
    "$event": "opentag",
    "data": [
      "title",
      {},
      false,
    ],
    "endIndex": 177,
    "startIndex": 171,
  },
  {
    "$event": "text",
    "data": [
      "Test",
    ],
    "endIndex": 181,
    "startIndex": 178,
  },
  {
    "$event": "closetag",
    "data": [
      "title",
      false,
    ],
    "endIndex": 189,
    "startIndex": 182,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 192,
    "startIndex": 190,
  },
  {
    "$event": "opentagname",
    "data": [
      "animate",
    ],
    "endIndex": 201,
    "startIndex": 193,
  },
  {
    "$event": "opentag",
    "data": [
      "animate",
      {},
      false,
    ],
    "endIndex": 203,
    "startIndex": 193,
  },
  {
    "$event": "closetag",
    "data": [
      "animate",
      true,
    ],
    "endIndex": 203,
    "startIndex": 193,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 206,
    "startIndex": 204,
  },
  {
    "$event": "opentagname",
    "data": [
      "polygon",
    ],
    "endIndex": 215,
    "startIndex": 207,
  },
  {
    "$event": "opentag",
    "data": [
      "polygon",
      {},
      false,
    ],
    "endIndex": 217,
    "startIndex": 207,
  },
  {
    "$event": "closetag",
    "data": [
      "polygon",
      true,
    ],
    "endIndex": 217,
    "startIndex": 207,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 220,
    "startIndex": 218,
  },
  {
    "$event": "opentagname",
    "data": [
      "g",
    ],
    "endIndex": 223,
    "startIndex": 221,
  },
  {
    "$event": "opentag",
    "data": [
      "g",
      {},
      false,
    ],
    "endIndex": 223,
    "startIndex": 221,
  },
  {
    "$event": "text",
    "data": [
      "
			",
    ],
    "endIndex": 227,
    "startIndex": 224,
  },
  {
    "$event": "opentagname",
    "data": [
      "path",
    ],
    "endIndex": 233,
    "startIndex": 228,
  },
  {
    "$event": "opentag",
    "data": [
      "path",
      {},
      false,
    ],
    "endIndex": 233,
    "startIndex": 228,
  },
  {
    "$event": "text",
    "data": [
      "
				",
    ],
    "endIndex": 238,
    "startIndex": 234,
  },
  {
    "$event": "opentagname",
    "data": [
      "title",
    ],
    "endIndex": 245,
    "startIndex": 239,
  },
  {
    "$event": "opentag",
    "data": [
      "title",
      {},
      false,
    ],
    "endIndex": 245,
    "startIndex": 239,
  },
  {
    "$event": "text",
    "data": [
      "x",
    ],
    "endIndex": 246,
    "startIndex": 246,
  },
  {
    "$event": "closetag",
    "data": [
      "title",
      false,
    ],
    "endIndex": 254,
    "startIndex": 247,
  },
  {
    "$event": "text",
    "data": [
      "
				",
    ],
    "endIndex": 259,
    "startIndex": 255,
  },
  {
    "$event": "opentagname",
    "data": [
      "animate",
    ],
    "endIndex": 268,
    "startIndex": 260,
  },
  {
    "$event": "opentag",
    "data": [
      "animate",
      {},
      false,
    ],
    "endIndex": 270,
    "startIndex": 260,
  },
  {
    "$event": "closetag",
    "data": [
      "animate",
      true,
    ],
    "endIndex": 270,
    "startIndex": 260,
  },
  {
    "$event": "text",
    "data": [
      "
			",
    ],
    "endIndex": 274,
    "startIndex": 271,
  },
  {
    "$event": "closetag",
    "data": [
      "path",
      false,
    ],
    "endIndex": 281,
    "startIndex": 275,
  },
  {
    "$event": "text",
    "data": [
      "
		",
    ],
    "endIndex": 284,
    "startIndex": 282,
  },
  {
    "$event": "closetag",
    "data": [
      "g",
      false,
    ],
    "endIndex": 288,
    "startIndex": 285,
  },
  {
    "$event": "text",
    "data": [
      "
	",
    ],
    "endIndex": 290,
    "startIndex": 289,
  },
  {
    "$event": "closetag",
    "data": [
      "svg",
      false,
    ],
    "endIndex": 296,
    "startIndex": 291,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 297,
    "startIndex": 297,
  },
  {
    "$event": "closetag",
    "data": [
      "body",
      false,
    ],
    "endIndex": 304,
    "startIndex": 298,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 305,
    "startIndex": 305,
  },
  {
    "$event": "closetag",
    "data": [
      "html",
      false,
    ],
    "endIndex": 312,
    "startIndex": 306,
  },
  {
    "$event": "text",
    "data": [
      "
",
    ],
    "endIndex": 313,
    "startIndex": 313,
  },
]
`;
