<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>Node-webkit-based module test</title>
<script>
function nwModuleTest(){
   var util = require('util');
   var moduleFolder = require('nw.gui').App.argv[0];
   try {
      require(moduleFolder);
   } catch(e) {
      if( process.platform !== 'win32' ){
         util.log('nw-pre-gyp error:');
         util.log(e.stack);
      }
      process.exit(1);
   }
   process.exit(0);
}
</script>
</head>
<body onload="nwModuleTest()">
<h1>Node-webkit-based module test</h1>
</body>
</html>
