import { baseRequestClient, requestClient } from '#/api/request';

/**
 * 登录
 */
export async function loginApi(data) {
  return requestClient.post('/auth/login', data);
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
  return baseRequestClient.post('/auth/refresh', {
    withCredentials,
  });
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return baseRequestClient.post('/auth/logout', {
    withCredentials,
  });
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi() {
  return requestClient.get('/auth/codes');
}
