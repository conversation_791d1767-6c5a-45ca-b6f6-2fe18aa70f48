// import type {
//   VbenFormSchema as FormSchema,
//   VbenFormProps,
// } from '@vben/common-ui';

// import type { ComponentType } from './component';

import { setupVbenForm, useVbenForm as useForm, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

async function initSetupVbenForm() {
  setupVbenForm({
    config: {
      // 一些组件是 v-model, Radio, Switch, Upload
    },
    defineRules: {
      required: (value, _params, ctx) => {
        if (value === undefined || value === null || value.length === 0) {
          return $t('ui.formRules.required', [ctx.label]);
        }
        return true;
      },
      // 选择项目必填国际化适配
      selectRequired: (value, _params, ctx) => {
        if (value === undefined || value === null) {
          return $t('ui.formRules.selectRequired', [ctx.label]);
        }
        return true;
      },
    },
  });
}

const useVbenForm = useForm;

export { initSetupVbenForm, useVbenForm, z };

// export type VbenFormSchema = FormSchema;
// export type { VbenFormProps };
