{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["pnpm-lock.yaml", "**/.env.*local", "internal/node-utils/*.json", "internal/node-utils/src/**/*.js", "internal/tailwind-config/src/**/*.js", "internal/vite-config/*.json", "internal/vite-config/src/**/*.js", "scripts/*/src/**/*.js", "scripts/*/src/**/*.json"], "globalEnv": ["NODE_ENV"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "dist.zip"]}, "preview": {"dependsOn": ["^build"], "outputs": ["dist/**"]}, "build:analyze": {"dependsOn": ["^build"], "outputs": ["dist/**"]}, "test:e2e": {}, "dev": {"dependsOn": [], "outputs": [], "cache": false, "persistent": true}}}