#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 递归查找所有 .vue 和 .js 文件
function findFiles(dir, extensions = ['.vue', '.js']) {
  let results = [];
  const list = fs.readdirSync(dir);

  list.forEach((file) => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat && stat.isDirectory()) {
      // 跳过 node_modules 和 dist 目录
      if (!['node_modules', 'dist', '.git'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });

  return results;
}

// 修复文件中的 TypeScript 类型导入
function fixTypeScriptImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 修复 import type 语句
    const typeImportRegex =
      /^(\s*)import\s+type\s+\{[^}]*\}\s+from\s+[^;]+;?\s*$/gm;
    content = content.replace(typeImportRegex, (match, indent) => {
      modified = true;
      const lines = match.split('\n');
      return lines
        .map((line) => {
          if (line.trim()) {
            return `${indent}// ${line.trim()}`;
          }
          return line;
        })
        .join('\n');
    });

    // 修复变量声明中的类型注解
    const varTypeRegex = /^(\s*)(const|let|var)\s+([^:=]+):\s*([^=]+)(\s*=)/gm;
    content = content.replace(
      varTypeRegex,
      (match, indent, keyword, varName, type, equals) => {
        modified = true;
        return `${indent}${keyword} ${varName.trim()}${equals}`;
      },
    );

    // 修复函数参数中的类型注解
    const funcParamTypeRegex = /function\s+([^(]+)\(([^)]*)\)/g;
    content = content.replace(funcParamTypeRegex, (match, funcName, params) => {
      if (params.includes(':')) {
        modified = true;
        const cleanParams = params
          .split(',')
          .map((param) => {
            const colonIndex = param.indexOf(':');
            if (colonIndex !== -1) {
              return param.substring(0, colonIndex).trim();
            }
            return param.trim();
          })
          .join(', ');
        return `function ${funcName}(${cleanParams})`;
      }
      return match;
    });

    // 修复箭头函数参数中的类型注解
    const arrowFuncTypeRegex = /\(([^)]*)\)\s*=>/g;
    content = content.replace(arrowFuncTypeRegex, (match, params) => {
      if (params.includes(':')) {
        modified = true;
        const cleanParams = params
          .split(',')
          .map((param) => {
            const colonIndex = param.indexOf(':');
            if (colonIndex !== -1) {
              return param.substring(0, colonIndex).trim();
            }
            return param.trim();
          })
          .join(', ');
        return `(${cleanParams}) =>`;
      }
      return match;
    });

    // 修复数组声明中的类型注解
    const arrayTypeRegex = /^(\s*)(const|let|var)\s+([^:=]+)\[\]\s*=/gm;
    content = content.replace(
      arrayTypeRegex,
      (match, indent, keyword, varName) => {
        modified = true;
        return `${indent}${keyword} ${varName.trim()} =`;
      },
    );

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed: ${filePath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// 主函数
function main() {
  const srcDir = path.join(__dirname, 'apps/web-antd/src');

  if (!fs.existsSync(srcDir)) {
    console.error('Source directory not found:', srcDir);
    process.exit(1);
  }

  console.log('Finding files to process...');
  const files = findFiles(srcDir);
  console.log(`Found ${files.length} files to process`);

  let fixedCount = 0;
  files.forEach((file) => {
    if (fixTypeScriptImports(file)) {
      fixedCount++;
    }
  });

  console.log(`\nProcessing complete!`);
  console.log(`Fixed ${fixedCount} files out of ${files.length} total files`);
}

// 直接运行主函数
main();
